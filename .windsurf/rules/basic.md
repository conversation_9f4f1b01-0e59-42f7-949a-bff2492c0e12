---
trigger: always_on
---

## CRITICAL PROJECT RULES (Always Follow First)

**Before writing ANY code:**
1. **Check for existing patterns** - Search codebase for similar functionality using file/function names
2. **Never modify .env** without explicit confirmation from developer
3. **No mocking data** outside of tests (dev/prod use real data only)
4. **File size limit** - Refactor any file approaching 300 lines
5. **Avoid one-off solutions** - If experiencing bugs, find systematic fixes, not hacks

## Project Overview

**Luno** - A monorepo containing:
- **Backend**: Express + TypeScript + TypeORM
- **Frontend**: Vite + React Router + TypeScript  
- **Database**: PostgreSQL (see `backend/docs/database-diagram.md`)

**Core Philosophy:**
- Simple, minimal solutions over complex abstractions
- Systematic fixes over quick hacks
- Code reuse through careful pattern analysis

## Development Workflow

### Before Making Changes
1. If the user request is ambigous and asking a single question would significantly improve the chances of a better response, ask before commencing work.
2. If user requests a new feature, ask up to 3 questions that would sinigifanctly affect your work.
3. **Pattern Check**: Search for similar implementations in existing codebase
4. **Database Changes**: 
   - Register new migrations in migrations file
   - Add new models to entities file
5. **Test Baseline**: Run existing tests to establish current state

### Implementation Process
1. **Reuse First**: If duplicating >10 lines, extract to shared utility
2. **Extend Existing**: Check if current utilities can be enhanced vs creating new ones
3. **Follow Patterns**: Use existing service patterns (see `UserService.ts` as template)

### Testing Strategy
- Write tests for all new business logic
- Test error states and edge cases
- Mock only external dependencies, never internal data flows

### Debugging Protocol
1. Find systematic root cause, avoid band-aid fixes
2. Find systematic root cause, avoid band-aid fixes
3. Find systematic root cause, avoid band-aid fixes

## Code Standards

### TypeScript & Documentation
- **JSDoc all functions** with params, returns, and purpose
- **Use interfaces** for props/state: `interface ComponentProps`
- **No `any` types** - use explicit types, generics, or `unknown`
- **Follow established patterns** found in existing similar components

### Clean Code Principles
- **Early returns** for error states and edge cases
- **DRY principle** following SOLID architecture
- **Single-purpose functions** (<50 lines, one responsibility)
- **Descriptive naming** that reveals intent
- **Event handlers** prefixed with "handle": `handleSubmit`, `handleClick`
- **Accessibility** features for all interactive elements

### Error Handling
- **Try/catch** for all async operations
- **Structured errors**: `{message: string, code: string, field?: string}`
- **Early returns** for error states to avoid nesting
- **User-friendly messages** that guide next actions

## Naming Conventions

| Context | Convention | Example |
|---------|------------|---------|
| Directories/Files | kebab-case | `auth-form.tsx`, `components/auth-form` |
| Components | PascalCase + named exports | `export function AuthForm` |
| Variables/Functions | camelCase | `userName`, `handleSubmit` |
| Interfaces | PascalCase | `interface UserProps` |
| Environment Variables | SNAKE_CASE | `API_KEY` |
| Database Columns | camelCase | `firstName` |
| CRUD Operations | Standard | `findAll`, `create`, `findOne`, `update`, `remove` |

## Architecture

### Frontend Structure
- **Components**: 
  - Page-specific components live in pages structure
  - Global components only if used in 3+ different contexts
- **Routing**: Register/verify routes in both frontend and backend
- **API Communication**: Use `lib/api` for all backend calls
- **Styling**: Tailwind classes only (no CSS files or inline styles)
- **UI Framework**: Leverage Shadcn UI components
- **React Pattern**: Functional components with hooks only
- **Performance**: `useMemo`/`useCallback` for expensive operations
- **Code Splitting**: Wrap dynamic imports with Suspense + fallback UI

### Backend Structure
- **Controllers**: Keep simple (routing + validation), delegate to services
- **Services**: Business logic, database operations, complex transformations
- **Class Limits**: Max 200 lines, 10 public functions, 10 properties
- **Database**: TypeORM with explicit entity relationships

### Permission System
**Format**: `resource:action:scope`

**Examples**: 
- `user:read:own` - Read own user data
- `project:delete:team` - Delete team projects
- `admin:*:*` - Admin access to everything

**Implementation**:
```typescript
// Single permission
@hasPermission('user:read:own')

// OR logic - needs any one permission
@hasAnyPermission(['user:read:own', 'user:read:team'])

// AND logic - needs all permissions
@hasAllPermissions(['project:read:team', 'user:read:team'])
```

### Design System
- **Backgrounds**: Neutral light tones
- **Borders**: Uniform border-radius across components
- **Elevation**: Consistent shadow/depth approach
- **Colors**: Defined system for categories and statuses
- **Dividers**: Subtle gray separators
- **Cards**: Minimal usage, only for distinct content groupings

## Additional Guidelines

### Decision Process
1. **When unsure about patterns**: Ask to see relevant existing files first
2. **Explain reasoning**: Always justify why choosing one approach over alternatives
3. **Show structure changes**: For refactoring, provide before/after code organization
4. **Diagnostic transparency**: When debugging, explain your reasoning process

### Conflict Resolution
When project rules conflict, prioritize in this order:
1. **Security** (permissions, data validation)
2. **Critical project rules** (no .env changes, no mocking)
3. **Maintainability** (file size, DRY principles)
4. **Performance** (early returns, memoization)
5. **Consistency** (naming, patterns)

### Code Review Checklist
Before suggesting code, verify:
- Searched for existing similar functionality
- Follows established patterns in codebase
- Includes proper TypeScript types
- Has appropriate error handling
- Uses correct naming conventions
- Stays under file size limits
- Includes JSDoc documentation

### Communication Style
- **Be specific**: "Use the pattern from `UserService.ts`" not "follow existing patterns"
- **Show examples**: Provide code snippets for complex explanations
- **Ask clarifying questions**: When requirements could be interpreted multiple ways
- **Suggest alternatives**: When proposing solutions, mention trade-offs