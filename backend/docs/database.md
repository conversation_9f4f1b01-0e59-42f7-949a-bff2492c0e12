# Luno Database Documentation

## Entity Relationship Diagram

```mermaid
erDiagram
    Organization ||--o{ User : "has many"
    Organization ||--o{ Team : "has many"
    Organization ||--o{ Role : "has many"
    Organization ||--o{ Conversation : "has many"
    Organization ||--o{ Contact : "has many"
    Organization ||--o{ Tag : "has many"
    Organization ||--o{ Project : "has many"
    Organization ||--o{ ConversationSource : "has many"
    
    Team ||--o{ User : "has many"
    Team ||--o{ Conversation : "has many"
    Team }o--o{ Project : "belongs to many"
    Team ||--o{ Playbook : "has many"
    
    Role ||--o{ User : "has many"
    Role ||--o{ RolePermission : "has many"
    
    Permission ||--o{ RolePermission : "has many"
    
    User ||--o{ Conversation : "owns"
    User ||--o{ Playbook : "creates"
    
    Conversation ||--o{ ConversationParticipant : "has many"
    Conversation ||--o{ ConversationEmbedding : "has many"
    Conversation ||--o{ Insight : "has many"
    Conversation }o--o{ Tag : "has many"
    Conversation }o--o{ Contact : "has many"
    Conversation ||--o{ ContactEvent : "referenced in"
    
    Contact ||--o{ ContactEvent : "has many"
    Contact ||--o{ ConversationParticipant : "participates as"
    
    ConversationSource ||--o{ ContactEvent : "referenced in"
    
    Project ||--o{ InsightDefinition : "has many"
    
    InsightDefinition ||--o{ Insight : "has many"
    
    RolePermission }|--|| Role : "belongs to"
    RolePermission }|--|| Permission : "belongs to"
    
    OAuthState ||--|| ConversationSource : "associated with"
```

## Core Entities

### Organization
The top-level entity that represents a company or organization using the Luno platform.

**Key Relationships:**
- Has many Users
- Has many Teams
- Has many Roles
- Has many Conversations
- Has many Projects

### User
Represents a user of the Luno platform.

**Key Attributes:**
- email (unique)
- name
- password (hashed)
- profileImagePath
- twoFactorEnabled

**Key Relationships:**
- Belongs to an Organization
- Belongs to a Team (optional)
- Has a Role (for RBAC)
- Owns many Conversations
- Creates Playbooks

### Team
Represents a group of users within an organization.

**Key Attributes:**
- name
- description
- color
- teamType

**Key Relationships:**
- Belongs to an Organization
- Has many Users
- Has many Conversations
- Belongs to many Projects
- Has many Playbooks

### Role
Defines a set of permissions for users.

**Key Attributes:**
- name
- description

**Key Relationships:**
- Belongs to an Organization
- Has many Users
- Has many Permissions (through RolePermission)

### Permission
Defines an action that can be performed on a resource.

**Key Attributes:**
- name (format: resource:action:scope)
- resource
- action
- scope ('all', 'team', or 'own')
- description

**Key Relationships:**
- Belongs to many Roles (through RolePermission)

### RolePermission
Junction entity that connects Roles and Permissions.

**Key Relationships:**
- Belongs to a Role
- Belongs to a Permission

## Conversation-Related Entities

### Conversation
Represents a conversation recorded in the system.

**Key Attributes:**
- externalId
- raw
- conversationTimestamped
- medium ('call', 'video', 'chat')
- processingStatus
- extracted_data
- messages

**Key Relationships:**
- Belongs to an Organization
- Belongs to a Team (optional)
- Belongs to an Owner (User)
- Has many Participants
- Has many Tags
- Has many Insights
- Related to many Contacts
- Referenced in ContactEvents

### ConversationParticipant
Represents a participant in a conversation.

**Key Attributes:**
- participantType ('user' or 'contact')
- participantId

**Key Relationships:**
- Belongs to a Conversation
- May be linked to a User or Contact

### ConversationEmbedding
Stores vector embeddings for conversation search and analysis.

**Key Attributes:**
- embedding
- content

**Key Relationships:**
- Belongs to a Conversation

### ConversationSource
Represents a source of conversations (e.g., integration).

**Key Attributes:**
- sourceId

**Key Relationships:**
- Belongs to an Organization
- Referenced in ContactEvents
- Associated with OAuthState

## Contact Management

### Contact
Represents a customer or external contact.

**Key Attributes:**
- name
- email (unique within organization)
- phone (unique within organization)

**Key Relationships:**
- Belongs to an Organization
- Has many ContactEvents
- Participates in many Conversations (through ConversationParticipant)

### ContactEvent
Records events related to contacts.

**Key Attributes:**
- title
- description
- eventType
- metadata (JSON)

**Key Relationships:**
- Belongs to a Contact
- May be linked to a Conversation
- May be linked to a ConversationSource

## Insights and Analytics

### Project
Represents a collection of insight definitions.

**Key Attributes:**
- name

**Key Relationships:**
- Belongs to an Organization
- Belongs to many Teams
- Has many InsightDefinitions

### InsightDefinition
Defines a type of insight to extract from conversations.

**Key Attributes:**
- name
- type
- config (JSON)

**Key Relationships:**
- Belongs to many Projects
- Has many Insights

### Insight
Represents an insight extracted from a conversation.

**Key Attributes:**
- result (JSON data)

**Key Relationships:**
- Belongs to a Conversation
- Belongs to an InsightDefinition

### Tag
Used to categorize conversations.

**Key Attributes:**
- name
- color

**Key Relationships:**
- Belongs to an Organization
- Applied to many Conversations

## Additional Entities

### Playbook
Represents a collection of guidance and best practices for teams.

**Key Attributes:**
- version
- content
- createdAt
- updatedAt

**Key Relationships:**
- Belongs to a Team
- Created by a User

### OAuthState
Stores OAuth state tokens for authentication flows.

**Key Attributes:**
- state
- sourceId
- expiresAt

**Key Relationships:**
- Associated with a ConversationSource

## RBAC System

The Role-Based Access Control system is implemented through the following entities:

### Role
Defines a set of permissions for users.

### Permission
Defines what actions can be performed on which resources and at what scope.

### RolePermission
Junction table that connects roles to permissions.

The RBAC system implements a hierarchical scope model with three levels:
1. **Organization-wide access** (`all` scope)
2. **Team-level access** (`team` scope)
3. **Individual user access** (`own` scope)

This hierarchy is strictly enforced, where higher-level scopes automatically grant access to lower-level scopes. For example, a user with `all` scope for a resource can also access that resource at the `team` and `own` scopes.

## Database Constraints

1. **Unique Constraints:**
   - User email is unique
   - Contact email and phone are unique within an organization
   - Permission name is unique
   - Role-Permission combinations are unique

2. **Cascading Deletes:**
   - When an Organization is deleted, all related entities are deleted
   - When a Conversation is deleted, all related Insights are deleted

3. **Nullable Relationships:**
   - User-Team relationship is optional (nullable)
   - Conversation-Team relationship is optional (nullable)

## Best Practices

1. **Always use the AppDataSource:**
   ```typescript
   import { AppDataSource } from "../data-source";
   
   // Example: Get user repository
   const userRepository = AppDataSource.getRepository(User);
   ```

2. **Use TypeORM relationships:**
   ```typescript
   // Example: Get a user with their team and role
   const user = await userRepository.findOne({
     where: { id: userId },
     relations: ['team', 'role']
   });
   ```

3. **Maintain data integrity:**
   - Always check for required relationships
   - Use transactions for operations that modify multiple entities
   - Validate data before saving to the database
