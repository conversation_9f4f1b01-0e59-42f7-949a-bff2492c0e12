# Adding New Conversation Sources to Luno

This document provides a technical guide for implementing new conversation source integrations in the Luno platform. It covers both backend and frontend implementation details.

## Overview

Conversation sources are external services (like OpenPhone, Zoom, Hubspot, etc.) that provide conversation data to Luno. Each source requires:

1. A backend service implementation that handles API communication
2. Registration in the source registry
3. Frontend UI support for configuration
4. User mapping functionality
5. Webhook handling for real-time updates (if supported by the source)

## 1. Backend Implementation

### 1.1. Create a Source Service

Each source must implement the `BaseSource` interface with required methods. Create a new file in `backend/src/services/conversation/sources/[source-name].ts`:

```typescript
import axios from 'axios';
import { ConversationSource } from '../../../models/conversationsource';
import { AppDataSource } from '../../../data-source';
import { BaseSource } from './base-source.interface';

export class NewSourceService implements BaseSource {
  private readonly BASE_URL = 'https://api.example.com/v1';
  private readonly repository = AppDataSource.getRepository(ConversationSource);
  private apiKey: string | null = null;

  /**
   * Set API key for the service
   */
  public setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Get the current API key
   */
  public getCredentials(): { apiKey: string | null } {
    return { apiKey: this.apiKey };
  }

  /**
   * Validate that API key exists and is set
   */
  private validateApiKeyExists(): string {
    if (!this.apiKey) {
      const error = new Error('API key not set');
      console.error(error);
      throw error;
    }
    return this.apiKey;
  }

  /**
   * Save source credentials
   */
  public async saveCredentials(sourceId: number, credentials: any) {
    const source = await this.repository.findOneBy({ id: sourceId });
    
    if (!source) {
      throw new Error('Source not found');
    }

    source.credentials = {
      apiKey: credentials.apiKey
      // Add other credentials as needed
    };

    this.setApiKey(credentials.apiKey);
    await this.repository.save(source);
  }

  /**
   * Validate connection to source
   * REQUIRED by BaseSource interface
   */
  public async validateConnection(): Promise<boolean> {
    try {
      const apiKey = this.validateApiKeyExists();
      const response = await axios.get(`${this.BASE_URL}/endpoint-to-validate`, {
        headers: {
          'Authorization': apiKey,
          'Content-Type': 'application/json'
        }
      });
      
      return response.status === 200;
    } catch (error) {
      console.error('Failed to validate connection:', error);
      return false;
    }
  }

  /**
   * List users from the source
   * REQUIRED by BaseSource interface
   */
  public async listUsers(params: any = {}): Promise<any[]> {
    try {
      const apiKey = this.validateApiKeyExists();
      const response = await axios.get(`${this.BASE_URL}/users-endpoint`, {
        headers: {
          'Authorization': apiKey,
          'Content-Type': 'application/json'
        },
        params
      });
      
      // Transform the response to a standard format
      // This will vary by source
      return response.data.users || [];
    } catch (error) {
      console.error('Failed to fetch users:', error);
      return [];
    }
  }

  /**
   * Add additional methods as needed for the specific source
   */
  public async getConversations(params: any = {}) {
    try {
      const apiKey = this.validateApiKeyExists();
      // Implement source-specific logic
      // ...
    } catch (error) {
      console.error('Failed to fetch conversations:', error);
      return { conversations: [] };
    }
  }
}
```

### 1.2. Register in the Source Factory

Add your new source to the source factory in `backend/src/services/conversation/sources/source-factory.ts`:

```typescript
import { NewSourceService } from './new-source';

// Inside the createSourceService method, add your source:
static createSourceService(sourceType: string, credentials?: any): BaseSource {
  switch (sourceType) {
    case 'openphone': {
      const service = new OpenPhoneService();
      if (credentials?.apiKey) {
        service.setApiKey(credentials.apiKey);
      }
      return service;
    }
    case 'new-source': {
      const service = new NewSourceService();
      if (credentials?.apiKey) {
        service.setApiKey(credentials.apiKey);
      }
      return service;
    }
    // Other sources...
    default:
      throw new Error(`Unsupported source type: ${sourceType}`);
  }
}
```

### 1.3. Add to the Source Registry

Register your source in `backend/src/services/conversation/sources/registry.ts`:

```typescript
export const adapterRegistry: Record<string, AdapterMetadata> = {
  // Existing sources...
  
  'new-source': {
    id: 'new-source',
    name: 'New Source Name',
    description: 'Import conversations from New Source',
    iconUrl: '/assets/images/sources/new-source.svg',
    authType: 'apikey', // or 'oauth', 'token'
    features: {
      audio: true,
      text: true,
      video: false
    }
  }
};
```

### 1.4. Implement OAuth Flow (if needed)

If your source uses OAuth instead of API keys, implement the OAuth flow:

```typescript
/**
 * Generate OAuth authorization URL
 */
public getAuthorizationUrl(state: string): string {
  const params = new URLSearchParams({
    client_id: this.config.clientId,
    redirect_uri: this.config.redirectUri,
    scope: this.config.scopes.join(' '),
    state,
    response_type: 'code'
  });

  return `${this.config.authUrl}?${params.toString()}`;
}

/**
 * Exchange authorization code for access token
 */
public async exchangeCodeForToken(code: string) {
  const params = new URLSearchParams({
    grant_type: 'authorization_code',
    client_id: this.config.clientId,
    client_secret: this.config.clientSecret,
    redirect_uri: this.config.redirectUri,
    code
  });

  const response = await axios.post(this.config.tokenUrl, params.toString(), {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });

  return response.data;
}
```

### 1.5. Add Configuration to Source Config

Add the OAuth configuration in `backend/src/services/conversation/sources/config.ts`:

```typescript
export const sourceConfig = {
  // Existing configs...
  
  'new-source': {
    clientId: process.env.NEW_SOURCE_CLIENT_ID,
    clientSecret: process.env.NEW_SOURCE_CLIENT_SECRET,
    redirectUri: `${process.env.API_URL}/auth/new-source/callback`,
    scopes: ['scope1', 'scope2'],
    authUrl: 'https://api.example.com/oauth/authorize',
    tokenUrl: 'https://api.example.com/oauth/token'
  }
};
```

### 1.6. Implement Webhook Support

For sources that support webhooks, you need to implement a webhook processor:

#### 1.6.1. Create a Webhook Processor

Create a new file in `backend/src/services/conversation/webhooks/new-source-webhook-processor.ts`:

```typescript
import { BaseWebhookProcessor } from './base-webhook-processor';

/**
 * Webhook processor for New Source
 * @class
 * @description Processes webhooks from New Source
 */
export class NewSourceWebhookProcessor extends BaseWebhookProcessor {
    /**
     * Process a webhook payload from New Source
     * @param {Record<string, unknown>} payload - The webhook payload to process
     * @param {Record<string, string>} headers - The webhook request headers
     * @returns {Promise<boolean>} - A promise that resolves to true if the webhook was processed successfully
     */
    async processWebhook(payload: Record<string, unknown>, headers: Record<string, string>): Promise<boolean> {
        try {
            console.log('Processing New Source webhook:', payload);
            
            // Extract event type from payload
            const eventType = payload.event as string;
            
            if (!eventType) {
                console.error('Missing event type in New Source webhook payload');
                return false;
            }
            
            // Handle different event types
            switch (eventType) {
                case 'conversation.created':
                    await this.handleConversationCreated(payload);
                    break;
                case 'message.created':
                    await this.handleMessageCreated(payload);
                    break;
                case 'contact.created':
                    await this.handleContactCreated(payload);
                    break;
                default:
                    console.log(`Unhandled New Source event type: ${eventType}`);
                    break;
            }
            
            return true;
        } catch (error) {
            console.error('Error processing New Source webhook:', error);
            return false;
        }
    }
    
    /**
     * Handle conversation.created event
     * @param {Record<string, unknown>} payload - The webhook payload
     */
    private async handleConversationCreated(payload: Record<string, unknown>): Promise<void> {
        // Implementation would depend on how you want to handle conversation created events
        console.log('Handling New Source conversation created event:', payload);
        
        // Example: You might want to create a new conversation record in your database
        // const conversation = payload.data as Record<string, unknown>;
        // const conversationId = conversation.id as string;
        // Create conversation record...
    }
    
    /**
     * Handle message.created event
     * @param {Record<string, unknown>} payload - The webhook payload
     */
    private async handleMessageCreated(payload: Record<string, unknown>): Promise<void> {
        // Implementation would depend on how you want to handle message created events
        console.log('Handling New Source message created event:', payload);
        
        // Example: You might want to create a new message record in your database
        // const message = payload.data as Record<string, unknown>;
        // const conversationId = message.conversation_id as string;
        // const content = message.content as string;
        // Create message record...
    }
    
    /**
     * Handle contact.created event
     * @param {Record<string, unknown>} payload - The webhook payload
     */
    private async handleContactCreated(payload: Record<string, unknown>): Promise<void> {
        // Implementation would depend on how you want to handle contact created events
        console.log('Handling New Source contact created event:', payload);
        
        // Example: You might want to create a new contact record in your database
        // const contact = payload.data as Record<string, unknown>;
        // const contactId = contact.id as string;
        // Create contact record...
    }
}
```

#### 1.6.2. Register the Webhook Processor

Register your webhook processor in `backend/src/controllers/conversation/webhook.controller.ts`:

```typescript
import { NewSourceWebhookProcessor } from '../../services/conversation/webhooks/new-source-webhook-processor';

// Add this line at the top of the file with the other registrations
WebhookProcessorFactory.registerProcessor('new-source', NewSourceWebhookProcessor);
```

#### 1.6.3. Update Your Source Service

Add webhook support methods to your source service:

```typescript
/**
 * Generate a new webhook secret for this source
 * @param {number} sourceId - The ID of the source to generate a secret for
 * @returns {Promise<string>} - The generated webhook secret
 */
public async generateWebhookSecret(sourceId: number): Promise<string> {
  const source = await this.repository.findOneBy({ id: sourceId });
  
  if (!source) {
    throw new Error('Source not found');
  }

  // The WebhookController will handle generating webhookId and webhookSecret
  // This method is a convenience wrapper for the controller functionality
  return null;
}

/**
 * Register the webhook with the external service
 * @param {string} webhookUrl - The URL to register with the external service
 * @param {string} secret - The webhook secret for verification
 * @returns {Promise<boolean>} - Whether the registration was successful
 */
public async registerWebhookWithExternalService(webhookUrl: string, secret: string): Promise<boolean> {
  try {
    const apiKey = this.validateApiKeyExists();
    
    // This will vary depending on the external service's API
    const response = await axios.post(
      `${this.BASE_URL}/webhooks`,
      {
        url: webhookUrl,
        secret: secret,
        events: ['conversation.created', 'message.created', 'contact.created']
      },
      {
        headers: {
          'Authorization': apiKey,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return response.status === 200 || response.status === 201;
  } catch (error) {
    console.error('Failed to register webhook with external service:', error);
    return false;
  }
}
```

## 2. Frontend Integration

### 2.1. Add SVG Icon

Add your source's SVG icon to `frontend/public/assets/images/sources/new-source.svg`.

### 2.2. Update Source Configuration UI

The existing source configuration page at `frontend/pages/app/account/sources/[id].tsx` should work for your new source without modifications as long as you've properly registered it in the adapter registry.

### 2.3. Implement OAuth Redirect (if needed)

If your source uses OAuth, implement a redirect handler in the frontend:

```typescript
// In your form submit handler
const startOAuthFlow = async () => {
  if (isSubmitting) return;
  
  setIsSubmitting(true);
  try {
    const response = await api.post(`conversation-sources/${id}/oauth/start`, { sourceId: id });
    
    // Redirect to OAuth provider
    window.location.href = response.authUrl;
  } catch (err) {
    console.error('Failed to start OAuth flow:', err);
    setError((err as Error).message || "Failed to start OAuth flow");
  } finally {
    setIsSubmitting(false);
  }
};
```

### 2.4. Add Webhook Configuration UI

For sources that support webhooks, use the existing webhook UI:

```tsx
<TwoColumnFormField
  title="Webhook Configuration"
  description="Set up webhooks to receive real-time updates from this source."
>
  <div className="space-y-4">
    {/* Webhook status display */}
    {source.webhookStatus ? (
      <div className="flex items-center space-x-2">
        <Badge 
          variant={source.webhookStatus === 'active' ? 'success' : 'destructive'}
          className="capitalize"
        >
          {source.webhookStatus}
        </Badge>
        {source.webhookStatus === 'active' && (
          <Badge variant="outline" className="bg-green-50">
            <Check className="h-3 w-3 mr-1" />
            Configured
          </Badge>
        )}
      </div>
    ) : (
      <Alert variant="warning">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No webhook configured</AlertTitle>
        <AlertDescription>
          Configure a webhook to receive real-time updates from this source.
        </AlertDescription>
      </Alert>
    )}
    
    {/* Create/Regenerate webhook button */}
    <Button
      variant={source.webhookStatus === 'active' ? 'outline' : 'default'}
      onClick={async () => {
        setCreatingWebhook(true);
        try {
          const response = await api.post(`/conversation-sources/${source.id}/webhook`);
          setWebhookDetails(response.webhook);
          mutate(); // Refresh source data
          toast({
            title: "Webhook created",
            description: "Webhook has been created successfully",
          });
        } catch (error) {
          console.error("Failed to create webhook:", error);
          toast({
            title: "Failed to create webhook",
            description: "Please try again later",
            variant: "destructive",
          });
        } finally {
          setCreatingWebhook(false);
        }
      }}
      disabled={creatingWebhook}
    >
      {creatingWebhook ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {source.webhookStatus === 'active' ? 'Regenerating...' : 'Creating...'}
        </>
      ) : (
        <>
          <Bell className="mr-2 h-4 w-4" />
          {source.webhookStatus === 'active' ? 'Regenerate Webhook' : 'Create Webhook'}
        </>
      )}
    </Button>
    
    {/* Webhook details dialog */}
    {webhookDetails && (
      <Dialog open={!!webhookDetails} onOpenChange={() => setWebhookDetails(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Webhook Created</DialogTitle>
            <DialogDescription>
              Use the following details to configure your webhook in {source.name}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Webhook URL</Label>
              <div className="flex">
                <Input 
                  value={webhookDetails.url} 
                  readOnly 
                  className="rounded-r-none flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-l-none"
                  onClick={() => {
                    navigator.clipboard.writeText(webhookDetails.url);
                    setCopiedUrl(true);
                    setTimeout(() => setCopiedUrl(false), 2000);
                  }}
                >
                  {copiedUrl ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Webhook Secret</Label>
              <div className="flex">
                <Input 
                  type="password"
                  value={webhookDetails.secret} 
                  readOnly 
                  className="rounded-r-none flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-l-none"
                  onClick={() => {
                    navigator.clipboard.writeText(webhookDetails.secret);
                    setCopiedSecret(true);
                    setTimeout(() => setCopiedSecret(false), 2000);
                  }}
                >
                  {copiedSecret ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                This secret will only be shown once. Make sure to copy it now.
              </p>
            </div>
          </div>
          
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              You may need to register this webhook in your {source.name} account settings.
              Use the URL and secret above to configure the webhook.
            </AlertDescription>
          </Alert>
          
          <DialogFooter className="sm:justify-end">
            <Button 
              variant="outline" 
              onClick={() => setWebhookDetails(null)}
            >
              Close
            </Button>
            <Button 
              type="button"
              onClick={async () => {
                setRegistering(true);
                try {
                  // This would call your source's registerWebhookWithExternalService method
                  await api.post(`/conversation-sources/${source.id}/register-webhook`, {
                    url: webhookDetails.url,
                    secret: webhookDetails.secret
                  });
                  
                  toast({
                    title: "Webhook registered",
                    description: "Webhook has been registered with external service",
                  });
                  
                  setWebhookDetails(null);
                } catch (error) {
                  console.error("Failed to register webhook:", error);
                  toast({
                    title: "Failed to register webhook",
                    description: "Please try registering manually in your source account",
                    variant: "destructive",
                  });
                } finally {
                  setRegistering(false);
                }
              }}
              disabled={registering}
            >
              {registering ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Registering...
                </>
              ) : (
                <>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Register with {source.name}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )}
    
    {/* Instructions */}
    {source.webhookStatus === 'active' && (
      <div className="bg-[#000] p-4 rounded-lg border border-[#E6E3E0] flex items-start gap-3">
        <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
        <div>
          <p className="text-sm text-muted-foreground">
            If you need to re-configure the webhook in your {source.name} account,
            please use the Regenerate Webhook button to get a new URL and secret.
          </p>
        </div>
      </div>
    )}
  </div>
</TwoColumnFormField>
```

## 3. Testing New Sources

### 3.1. Backend Testing

Create tests for your source service in `backend/src/tests/services/conversation/sources/new-source.test.ts`:

```typescript
import { NewSourceService } from '../../../../services/conversation/sources/new-source';
import axios from 'axios';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock axios
vi.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('NewSourceService', () => {
  let service: NewSourceService;

  beforeEach(() => {
    service = new NewSourceService();
    service.setApiKey('test-api-key');
  });

  it('should validate connection successfully', async () => {
    mockedAxios.get.mockResolvedValueOnce({ status: 200, data: {} });
    
    const result = await service.validateConnection();
    
    expect(result).toBe(true);
    expect(mockedAxios.get).toHaveBeenCalledWith(
      expect.stringContaining('/endpoint-to-validate'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': 'test-api-key'
        })
      })
    );
  });

  it('should list users successfully', async () => {
    const mockUsers = [{ id: '1', name: 'Test User' }];
    mockedAxios.get.mockResolvedValueOnce({ 
      status: 200, 
      data: { users: mockUsers } 
    });
    
    const result = await service.listUsers();
    
    expect(result).toEqual(mockUsers);
  });

  // Add more tests as needed
});
```

### 3.2. Testing Webhooks

Create tests for your webhook processor:

```typescript
import { NewSourceWebhookProcessor } from '../../../../services/conversation/webhooks/new-source-webhook-processor';
import { describe, it, expect, vi, beforeEach } from 'vitest';

describe('NewSourceWebhookProcessor', () => {
  let processor: NewSourceWebhookProcessor;

  beforeEach(() => {
    processor = new NewSourceWebhookProcessor();
    
    // Mock the handler methods
    vi.spyOn(processor as any, 'handleConversationCreated').mockResolvedValue(undefined);
    vi.spyOn(processor as any, 'handleMessageCreated').mockResolvedValue(undefined);
    vi.spyOn(processor as any, 'handleContactCreated').mockResolvedValue(undefined);
  });

  it('should process conversation.created event', async () => {
    const payload = {
      event: 'conversation.created',
      data: { id: '123' }
    };
    
    const result = await processor.processWebhook(payload, {});
    
    expect(result).toBe(true);
    expect((processor as any).handleConversationCreated).toHaveBeenCalledWith(payload);
  });

  it('should process message.created event', async () => {
    const payload = {
      event: 'message.created',
      data: { id: '456', conversation_id: '123' }
    };
    
    const result = await processor.processWebhook(payload, {});
    
    expect(result).toBe(true);
    expect((processor as any).handleMessageCreated).toHaveBeenCalledWith(payload);
  });

  it('should verify signature correctly', () => {
    const payload = { id: '123', data: 'test' };
    const secret = 'test-secret';
    
    // Create a valid signature
    const crypto = require('crypto');
    const hmac = crypto.createHmac('sha256', secret);
    const signature = hmac.update(JSON.stringify(payload)).digest('hex');
    
    // Test verification
    expect(processor.verifySignature(payload, signature, secret)).toBe(true);
    expect(processor.verifySignature(payload, 'invalid-signature', secret)).toBe(false);
  });
});
```

### 3.3. Testing The Webhook Controller

Make sure your webhook processor is correctly registered and works with the webhook controller:

```typescript
import { WebhookController } from '../../../../controllers/conversation/webhook.controller';
import { WebhookProcessorFactory } from '../../../../services/conversation/webhooks/webhook-processor-factory';
import { describe, it, expect, vi, beforeEach } from 'vitest';

describe('WebhookController with NewSource', () => {
  it('should correctly register the NewSourceWebhookProcessor', () => {
    // Test registration - this would verify that the processor is registered
    // in the real system, you would mock the ConversationSource and test
    // that the correct processor is created
    const mockSource = { 
      id: 1, 
      type: 'new-source',
      webhookId: 'test-id',
      webhookSecret: 'test-secret'
    };
    
    const processor = WebhookProcessorFactory.createProcessor(mockSource as any);
    expect(processor).not.toBeNull();
    expect(processor).toBeInstanceOf(NewSourceWebhookProcessor);
  });
});
```

### 3.4. Manual Testing Checklist

1. **Source Registration**:
   - Confirm source appears in the sources list
   - Verify icon displays correctly

2. **Authentication**:
   - Test API key validation (or OAuth flow)
   - Verify error handling for invalid credentials

3. **User Mapping**:
   - Confirm external users are fetched correctly
   - Test mapping creation and deletion
   - Verify UI updates appropriately

4. **Webhook Configuration**:
   - Test creating and regenerating webhooks
   - Verify webhook URL and secret are correctly generated
   - Test registering the webhook with the external service
   - Manually trigger webhooks from the source system
   - Verify webhook events are properly processed

5. **Conversation Import**:
   - Test importing conversations from the source
   - Verify data integrity and format

## 4. RBAC Considerations

Ensure the source respects the RBAC system:

1. User mappings should be organization-scoped
2. Only users with appropriate permissions can access/modify source settings
3. Source operations should follow the established permissions model:
   - `conversation-sources:read` for reading sources and mappings
   - `conversation-sources:write` for creating/updating sources and mappings

## 5. Troubleshooting

### 5.1. Common Issues

- **API Rate Limiting**: Implement backoff strategies for sources with rate limits
- **Authentication Failures**: Double-check credentials format and validation logic
- **Data Transformation Errors**: Verify response parsing and transformation logic
- **Webhook Signature Verification**: Ensure webhook secrets are correctly stored and used

### 5.2. Logging

Implement comprehensive logging for debugging:

```typescript
try {
  // Operation
} catch (error) {
  console.error('Detailed error message:', error);
  // Include relevant context like sourceId, userId, etc.
}
```

## 6. Deployment Considerations

1. Add any required environment variables to your deployment environment
2. Update API documentation for the new source endpoints
3. Add OAuth callback URLs to the source's developer console (if applicable)
4. Configure firewall rules to allow incoming webhook requests
5. Consider gradual rollout for new sources to monitor performance

---

## Example: OpenPhone Implementation

For a complete reference implementation, see:
- `backend/src/services/conversation/sources/openphone.ts`
- `backend/src/services/conversation/sources/registry.ts`
- `backend/src/services/conversation/sources/source-factory.ts`
- `backend/src/controllers/conversation-source-mapping.controller.ts`
- `backend/src/services/conversation/webhooks/openphone-webhook-processor.ts`
