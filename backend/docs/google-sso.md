# Google SSO Integration

This document outlines how to set up and use Google SSO (Single Sign-On) with <PERSON><PERSON>.

## Overview

Luno supports Google SSO, allowing users to sign in with their Google accounts. This feature can be configured to:

1. Allow users from specific domains to automatically register and log in
2. Restrict self-registration based on domain

## Setup Instructions

### 1. Create Google OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" and select "OAuth client ID"
5. Set the application type to "Web application"
6. Add authorized JavaScript origins:
   - For development: `http://localhost:3000`
   - For production: Your production URL
7. Add authorized redirect URIs:
   - For development: `http://localhost:3000/app`
   - For production: Your production URL + `/app`
8. Click "Create" and note the Client ID

### 2. Configure Environment Variables

#### Frontend Configuration

Add the Google Client ID to your frontend `.env` file:

```
VITE_GOOGLE_CLIENT_ID=your-google-client-id
```

#### Backend Configuration

Add the Google Client ID to your backend `.env` file:

```
VITE_GOOGLE_CLIENT_ID=your-google-client-id
ALLOW_NEW_ORGANIZATIONS=false
```

Set `ALLOW_NEW_ORGANIZATIONS=true` if you want to allow users from unrecognized domains to create new organizations.

### 3. Configure Organization SSO Settings

To configure which domains can use SSO and whether they can self-register:

1. In the database, update the `organizations` table:
   - Set `allow_sso_self_registration` to `true` to allow users from that organization's domains to self-register
   - Add domains to the `sso_domains` array column

Example SQL:

```sql
-- Allow users from example.com to self-register
UPDATE organizations 
SET allow_sso_self_registration = true, 
    sso_domains = ARRAY['example.com']
WHERE id = 1;
```

## How It Works

1. Users click the "Sign in with Google" button on the login page
2. Google's authentication flow is initiated
3. After successful Google authentication, the Google ID token is sent to the backend
4. The backend verifies the token and extracts the user's email and domain
5. The backend checks if the domain is in any organization's `sso_domains` array
6. If a matching organization is found:
   - If `allow_sso_self_registration` is `true` and the user doesn't exist, a new user is created
   - If the user exists, they are logged in
   - If `allow_sso_self_registration` is `false` and the user doesn't exist, an error is returned
7. If no matching organization is found:
   - If `ALLOW_NEW_ORGANIZATIONS` is `true`, a new organization is created
   - If `ALLOW_NEW_ORGANIZATIONS` is `false`, an error is returned

## Error Handling

The following error cases are handled:

- Invalid Google token: Returns a 401 error
- Domain not allowed for self-registration: Returns a 403 error with `domain_not_allowed` error code
- Domain not recognized and new organizations not allowed: Returns a 403 error with `domain_not_recognized` error code

## Security Considerations

- The Google ID token is verified using Google's official libraries
- Users created via SSO have a random password generated
- JWT tokens issued by the backend have the same expiration as regular login tokens
