# Event Rendering System

## Overview

The Event Rendering System is an extensible architecture that standardizes how events are displayed across the Luno platform. It provides a consistent way to render events from different providers (Zoom, internal, etc.) while allowing for customization based on event type.

This document describes the architecture, components, and usage of the event rendering system implemented in commit `dda1c9196bc9e91fed81382bbd14199efaecd9b8`.

## Architecture

The event rendering system follows a registry pattern with the following components:

### Backend Components

1. **Event Registry**: Central registry that maps event types and providers to their respective renderers
2. **Event Renderers**: Implementations that convert raw event data into structured display data
3. **Event System Initialization**: Setup process that registers all renderers and event types

### Frontend Components

1. **Event Renderer Context**: React context that provides rendering capabilities to components
2. **Event Renderer Registry**: Similar to the backend registry, maps event types to renderer implementations
3. **Event Renderer Components**: React components that render events based on their type and provider

## Data Flow

1. Events are stored in the database with metadata specific to their type and provider
2. When events are requested with `?render=true`, the backend renderer processes each event
3. The frontend receives pre-rendered data or renders raw events client-side using the frontend renderer
4. UI components display the rendered event data consistently

## Backend Implementation

### Event Renderer Interface

```typescript
interface EventRenderer {
  render(event: ContactEvent): EventRenderData;
}

interface EventRenderData {
  title: string;
  description: string;
  icon: string;
  color: string;
  displayMetadata: Record<string, any>;
  actions: Array<{
    label: string;
    action: string;
    icon: string;
    requiresConfirmation?: boolean;
  }>;
}
```

### Event Registry

The event registry maintains mappings between event types, providers, and their renderers:

```typescript
class EventRendererRegistry {
  private renderers: Map<string, EventRenderer> = new Map();
  
  registerRenderer(eventType: string, provider: string, renderer: EventRenderer): void {
    const key = `${eventType}:${provider}`;
    this.renderers.set(key, renderer);
  }
  
  getRenderer(eventType: string, provider: string): EventRenderer | undefined {
    const key = `${eventType}:${provider}`;
    return this.renderers.get(key);
  }
}
```

### System Initialization

The event system is initialized during application startup in `app.ts`:

```typescript
// Initialize event system
initializeEventSystem();
```

## Frontend Implementation

### Event Renderer Context

A React context provides rendering capabilities to components:

```typescript
const EventRendererContext = createContext<EventRendererContextType | null>(null);

export const EventRendererProvider: React.FC = ({ children }) => {
  const registry = useMemo(() => {
    const reg = new EventRendererRegistry();
    // Register renderers here
    return reg;
  }, []);
  
  return (
    <EventRendererContext.Provider value={{ registry }}>
      {children}
    </EventRendererContext.Provider>
  );
};
```

### Event Renderer Components

Frontend renderers implement a common interface:

```typescript
interface EventRenderer {
  render(event: ContactEvent): React.ReactNode;
}
```

## Usage

### Rendering Events in the Backend

```typescript
// In controllers
const events = await ContactEventService.listByContact(contactId);
    
if (req.query.render === 'true') {
  const renderedEvents = events.map(event => ({
    ...event,
    rendered: ContactEventService.renderEvent(event)
  }));
  res.json(renderedEvents);
}
```

### Rendering Events in the Frontend

```typescript
const { renderEvent } = useEventRenderer();

// In a component
return (
  <div>
    {events.map(event => (
      <EventCard 
        key={event.id}
        event={event}
        renderedContent={renderEvent(event)}
      />
    ))}
  </div>
);
```

## Creating New Event Renderers

### Backend Renderer

1. Create a new renderer class implementing the `EventRenderer` interface
2. Register the renderer in `event-system.ts`

```typescript
// Example renderer for a contact_created event
export class ContactCreatedRenderer implements EventRenderer {
  render(event: ContactEvent): EventRenderData {
    return {
      title: 'Contact Created',
      description: `Contact ${event.metadata.name} was created`,
      icon: 'user-plus',
      color: '#10B981',
      displayMetadata: {
        'Name': event.metadata.name,
        'Email': event.metadata.email,
        'Created At': new Date(event.createdAt).toLocaleString()
      },
      actions: [
        {
          label: 'View Contact',
          action: 'view',
          icon: 'eye'
        }
      ]
    };
  }
}
```

### Frontend Renderer

1. Create a new renderer class implementing the frontend `EventRenderer` interface
2. Register the renderer in the `EventRendererProvider`

```typescript
export class ContactCreatedRenderer implements EventRenderer {
  render(event: ContactEvent): React.ReactNode {
    return (
      <div className="flex items-center gap-2">
        <UserPlus className="h-4 w-4 text-emerald-500" />
        <span>Contact {event.metadata.name} was created</span>
      </div>
    );
  }
}
```

## Helper Script

The commit includes a helper script `create-event-renderer.sh` that generates boilerplate for new event renderers:

```bash
./create-event-renderer.sh --event-type CONTACT_CREATED --provider internal --display-name "Contact Created"
```

## RBAC Considerations

When implementing new event types or renderers, ensure appropriate permission checks are in place for:

1. Creating events of specific types
2. Viewing events with sensitive information
3. Performing actions on events

Use the `usePermissions` hook in the frontend to conditionally render UI elements based on user permissions.
