# Database Schema Diagram

This diagram is automatically generated from the database schema using class notation. Do not edit manually.

Last updated: 2025-04-02T19:26:52.887Z

```mermaid
classDiagram
  class audit_logs {
    integer id (PK)
    integer userId?
    character varying action
    character varying resource
    character varying entityId?
    character varying ipAddress?
    character varying userAgent?
    jsonb requestData?
    jsonb responseData?
    jsonb changes?
    jsonb metadata?
    character varying status
    character varying errorMessage?
    timestamp without time zone createdAt
    integer organizationId?
  }
  class automation_connections {
    uuid id (PK)
    uuid automationId
    uuid sourceNodeId
    uuid targetNodeId
    character varying sourceHandle?
    character varying targetHandle?
    character varying label?
    character varying condition?
    jsonb metadata?
    jsonb uiSettings?
    boolean disabled
    character varying disabledReason?
    integer priority
  }
  class automation_executions {
    uuid id (PK)
    uuid automationId
    integer automationVersion
    character varying status
    integer triggeredById?
    character varying triggerType?
    jsonb triggerData
    integer organizationId
    timestamp without time zone startedAt
    timestamp without time zone completedAt?
    integer duration
    jsonb error?
    jsonb context
    jsonb result?
    jsonb metrics?
    integer retryCount
    character varying retryOf?
    character varying parentExecutionId?
  }
  class automation_node_executions {
    uuid id (PK)
    uuid executionId
    uuid branchExecutionId?
    uuid nodeId
    character varying nodeType
    character varying status
    timestamp without time zone startedAt
    timestamp without time zone completedAt?
    integer duration
    jsonb input
    jsonb output?
    jsonb error?
    integer retryCount
    jsonb retryHistory?
    timestamp without time zone nextRetryAt?
    character varying parentNodeExecutionId?
    jsonb executionPath?
  }
  class automation_nodes {
    uuid id (PK)
    character varying name
    character varying nodeType?
    jsonb position
    jsonb configuration
    jsonb uiSettings?
    jsonb metadata?
    uuid automationId
    uuid nodeTypeId
    boolean disabled
    character varying disabledReason?
    integer retryLimit
    jsonb retryConfig?
    jsonb timeoutConfig?
  }
  class automation_variables {
    uuid id (PK)
    character varying name
    character varying description?
    jsonb defaultValue?
    text encryptedValue?
    boolean isRequired
    boolean isSecret
    uuid automationId?
    integer organizationId?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class automations {
    uuid id (PK)
    character varying name
    character varying status
    text description?
    integer version
    integer createdById
    integer organizationId
    jsonb metadata?
    jsonb settings?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_invoices {
    integer id (PK)
    integer organizationId
    integer subscriptionId
    character varying stripeInvoiceId
    numeric amount
    character varying currency
    USER-DEFINED status
    timestamp without time zone dueDate
    timestamp without time zone paidAt?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_payment_methods {
    integer id (PK)
    integer organizationId
    character varying stripePaymentMethodId
    USER-DEFINED type
    boolean isDefault
    character varying last4?
    integer expiryMonth?
    integer expiryYear?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_plan_tiers {
    integer id (PK)
    integer planId
    character varying name
    numeric seatPrice
    integer minimumSeats
    integer maximumSeats?
    integer insightsPerSeat
    numeric transcriptionHoursPerSeat
    USER-DEFINED billingFrequency
    character varying stripePriceId
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_plans {
    integer id (PK)
    character varying name
    text description?
    boolean isActive
    character varying stripeProductId
    integer organizationId?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_subscriptions {
    integer id (PK)
    integer organizationId
    integer planTierId
    USER-DEFINED status
    character varying stripeSubscriptionId
    character varying stripeCustomerId
    timestamp without time zone currentPeriodStart
    timestamp without time zone currentPeriodEnd
    timestamp without time zone cancelAt?
    timestamp without time zone canceledAt?
    integer seats
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_usage_quotas {
    integer id (PK)
    integer organizationId
    integer subscriptionId
    timestamp without time zone periodStart
    timestamp without time zone periodEnd
    integer totalSeats
    integer allocatedSeats
    integer totalInsights
    integer usedInsights
    numeric totalTranscriptionHours
    numeric usedTranscriptionHours
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class billing_user_seat_allocations {
    integer id (PK)
    integer userId
    integer organizationId
    timestamp without time zone allocationDate
    timestamp without time zone deallocatedDate?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class branch_executions {
    uuid id (PK)
    uuid execution_id
    character varying branchId
    character varying name
    character varying status
    timestamp without time zone startedAt
    timestamp without time zone completedAt?
    integer duration
    jsonb context?
    jsonb result?
    jsonb error?
  }
  class contact_events {
    integer id (PK)
    character varying title
    text description?
    character varying eventType
    jsonb metadata?
    integer contactId
    integer conversationId?
    integer sourceId?
    integer createdById?
    timestamp without time zone eventDate
    timestamp without time zone created_at
    timestamp without time zone updated_at
    character varying provider?
  }
  class contacts {
    integer id (PK)
    character varying name
    character varying email?
    character varying phone?
    integer organizationId
    timestamp without time zone created_at
    timestamp without time zone updated_at
  }
  class conversation {
    integer id (PK)
    character varying externalId?
    text raw?
    jsonb extracted_data?
    timestamp without time zone created_at
    timestamp without time zone updated_at
    integer team_id?
    integer organizationId
    character varying medium
    jsonb messages?
    character varying processingStatus?
    integer ownerId?
    character varying recordingPath?
    character varying recordingContentType?
    jsonb conversationTimestamped?
    integer contactId?
  }
  class conversation_embeddings {
    integer id (PK)
    integer conversationId
    USER-DEFINED embedding?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
    text content?
  }
  class conversation_participants {
    integer id (PK)
    integer conversationId
    USER-DEFINED participantType
    integer participantId
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class conversation_source_mappings {
    integer id (PK)
    integer organizationId
    integer userId
    integer conversationSourceId
    character varying externalId
    jsonb externalMetadata?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class conversation_sources {
    integer id (PK)
    character varying name
    character varying type
    jsonb config?
    jsonb credentials?
    boolean is_active
    ARRAY enabled_features
    integer organizationId
    timestamp without time zone created_at
    timestamp without time zone updated_at
    character varying webhook_id?
    character varying webhook_secret?
    character varying webhook_status
    character varying preferred_medium?
  }
  class conversation_tags {
    integer conversation_id (PK)
    integer tag_id (PK)
    timestamp without time zone created_at
  }
  class copilot_messages {
    character varying id (PK)
    integer threadId
    character varying role
    jsonb content
    timestamp without time zone createdAt
  }
  class copilot_threads {
    integer id (PK)
    character varying title?
    text description?
    integer organizationId
    integer userId
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class demo_requests {
    uuid id (PK)
    character varying firstName
    character varying lastName
    character varying email
    character varying company
    character varying jobTitle?
    character varying companySize?
    text message?
    boolean contacted
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class insight_definitions {
    integer id (PK)
    character varying name
    character varying type
    jsonb config?
    timestamp without time zone created_at
    timestamp without time zone updated_at
    jsonb catalog?
  }
  class insights {
    integer id (PK)
    integer conversation_id
    integer definition_id
    jsonb result
    timestamp without time zone created_at
    timestamp without time zone updated_at
  }
  class migrations {
    integer id (PK)
    bigint timestamp
    character varying name
  }
  class node_categories {
    uuid id (PK)
    character varying key
    character varying name
    text description?
    character varying icon?
    character varying color?
    integer displayOrder
    boolean isEnabled
    boolean isSystem
    boolean isHidden
    jsonb metadata?
    character varying parentId?
    jsonb tags?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class node_type_versions {
    uuid id (PK)
    uuid nodeTypeId
    character varying version
    jsonb configurationSchema
    jsonb uiSchema?
    jsonb defaultConfiguration?
    boolean isDeprecated
    timestamp without time zone deprecatedAt?
    jsonb migrationNotes?
    jsonb changeLog?
    timestamp without time zone createdAt
  }
  class node_types {
    uuid id (PK)
    character varying key
    character varying name
    text description?
    jsonb configurationSchema?
    jsonb uiSchema?
    jsonb defaultConfiguration?
    boolean isEnabled
    integer displayOrder
    uuid categoryId
    jsonb metadata?
    jsonb capabilities?
    jsonb inputSchema?
    jsonb outputSchema?
    character varying icon?
    character varying documentationUrl?
    boolean isDeprecated
    timestamp without time zone deprecatedAt?
    boolean isExperimental
    boolean isHidden
    jsonb tags?
    character varying version?
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
    USER-DEFINED category?
  }
  class oauth_states {
    uuid id (PK)
    character varying state
    character varying source_id
    timestamp without time zone createdAt
    timestamp without time zone expiresAt?
  }
  class organizations {
    integer id (PK)
    character varying name
    timestamp without time zone created_at
    timestamp without time zone updated_at
    text handbook?
    boolean allow_sso_self_registration
    ARRAY sso_domains
    boolean sso_enabled
  }
  class permissions {
    integer id (PK)
    character varying name
    text description?
    character varying resource
    character varying action
    timestamp without time zone created_at
    timestamp without time zone updated_at
    USER-DEFINED scope
  }
  class playbooks {
    integer id (PK)
    integer teamId
    integer userId
    integer version
    text content
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
  }
  class project_insight_definitions {
    integer project_id (PK)
    integer definition_id (PK)
    integer order
  }
  class project_teams {
    integer project_id (PK)
    integer team_id (PK)
    timestamp without time zone created_at?
  }
  class projects {
    integer id (PK)
    character varying name
    timestamp without time zone created_at
    timestamp without time zone updated_at
    integer organizationId
    character varying language?
    text projectKnowledge?
  }
  class role_permissions {
    integer id (PK)
    integer roleId
    integer permissionId
    timestamp without time zone created_at
    timestamp without time zone updated_at
  }
  class roles {
    integer id (PK)
    character varying name
    text description?
    integer organizationId
    timestamp without time zone created_at
    timestamp without time zone updated_at
  }
  class tags {
    integer id (PK)
    character varying name
    timestamp without time zone created_at
    timestamp without time zone updated_at
    character varying color?
    integer organizationId
  }
  class teams {
    integer id (PK)
    integer id (PK)
    character varying name
    character varying name
    integer company_id
    text description?
    timestamp without time zone created_at
    character varying color?
    timestamp with time zone updated_at
    timestamp without time zone updated_at
    timestamp with time zone created_at
    integer organizationId
    character varying color?
    character varying teamType?
  }
  class user_sessions {
    uuid id (PK)
    integer userId
    character varying deviceName?
    character varying deviceType?
    character varying browser?
    character varying operatingSystem?
    character varying ipAddress?
    character varying location?
    boolean isCurrent
    character varying token
    timestamp without time zone createdAt
    timestamp without time zone updatedAt
    timestamp without time zone lastActiveAt
  }
  class users {
    integer id (PK)
    integer id (PK)
    character varying email
    character varying name
    integer company_id
    character varying password
    character varying name
    integer team_id?
    timestamp with time zone updated_at
    integer organizationId
    timestamp with time zone created_at
    timestamp without time zone created_at
    timestamp without time zone updated_at
    boolean enable_a_p_i_key?
    character varying api_key?
    integer team_id?
    character varying api_key_index?
    character varying profileImageType?
    character varying email
    character varying reset_password_token?
    integer lastViewedPlaybook?
    character varying profileImagePath?
    timestamp with time zone reset_password_expiration?
    integer roleId?
    character varying salt?
    character varying twoFactorSecret?
    character varying hash?
    numeric login_attempts?
    boolean twoFactorEnabled
    character varying backupCodes?
    timestamp with time zone lock_until?
  }
  audit_logs --|> users : userId
  audit_logs --|> organizations : organizationId
  automation_connections --|> automations : automationId
  automation_connections --|> automation_nodes : sourceNodeId
  automation_connections --|> automation_nodes : targetNodeId
  automation_executions --|> automations : automationId
  automation_executions --|> organizations : organizationId
  automation_executions --|> users : triggeredById
  automation_node_executions --|> branch_executions : branchExecutionId
  automation_node_executions --|> automation_nodes : nodeId
  automation_node_executions --|> automation_executions : executionId
  automation_nodes --|> automations : automationId
  automation_nodes --|> node_types : nodeTypeId
  automation_variables --|> organizations : organizationId
  automation_variables --|> automations : automationId
  automations --|> organizations : organizationId
  automations --|> users : createdById
  billing_invoices --|> organizations : organizationId
  billing_invoices --|> billing_subscriptions : subscriptionId
  billing_payment_methods --|> organizations : organizationId
  billing_plan_tiers --|> billing_plans : planId
  billing_plans --|> organizations : organizationId
  billing_subscriptions --|> organizations : organizationId
  billing_subscriptions --|> billing_plan_tiers : planTierId
  billing_usage_quotas --|> organizations : organizationId
  billing_usage_quotas --|> billing_subscriptions : subscriptionId
  billing_user_seat_allocations --|> users : userId
  billing_user_seat_allocations --|> organizations : organizationId
  branch_executions --|> automation_executions : execution_id
  contact_events --|> contacts : contactId
  contact_events --|> users : createdById
  contact_events --|> conversation_sources : sourceId
  contact_events --|> conversation : conversationId
  contacts --|> organizations : organizationId
  conversation --|> contacts : contactId
  conversation --|> users : ownerId
  conversation --|> teams : team_id
  conversation --|> organizations : organizationId
  conversation --|> teams : team_id
  conversation_embeddings --|> conversation : conversationId
  conversation_participants --|> conversation : conversationId
  conversation_source_mappings --|> organizations : organizationId
  conversation_source_mappings --|> users : userId
  conversation_source_mappings --|> conversation_sources : conversationSourceId
  conversation_sources --|> organizations : organizationId
  conversation_tags --|> conversation : conversation_id
  conversation_tags --|> tags : tag_id
  copilot_messages --|> copilot_threads : threadId
  copilot_threads --|> users : userId
  copilot_threads --|> organizations : organizationId
  insights --|> insight_definitions : definition_id
  insights --|> conversation : conversation_id
  node_type_versions --|> node_types : nodeTypeId
  payload_locked_documents_rels --|> posts : posts_id
  payload_locked_documents_rels --|> payload_locked_documents : parent_id
  payload_locked_documents_rels --|> users : users_id
  payload_locked_documents_rels --|> companies : companies_id
  payload_locked_documents_rels --|> teams : teams_id
  payload_preferences_rels --|> users : users_id
  payload_preferences_rels --|> payload_preferences : parent_id
  playbooks --|> teams : teamId
  playbooks --|> users : userId
  posts --|> users : author_id
  project_insight_definitions --|> insight_definitions : definition_id
  project_insight_definitions --|> projects : project_id
  project_teams --|> teams : team_id
  project_teams --|> projects : project_id
  projects --|> organizations : organizationId
  role_permissions --|> permissions : permissionId
  role_permissions --|> roles : roleId
  roles --|> organizations : organizationId
  tags --|> organizations : organizationId
  teams --|> companies : company_id
  teams --|> organizations : organizationId
  user_sessions --|> users : userId
  users --|> companies : company_id
  users --|> teams : team_id
  users --|> roles : roleId
  users --|> organizations : organizationId
  users --|> teams : team_id
```