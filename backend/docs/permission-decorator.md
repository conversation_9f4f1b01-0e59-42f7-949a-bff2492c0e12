# Permission Decorator Documentation

## Overview

The `@hasPermission` decorator provides a clean and declarative way to enforce RBAC (Role-Based Access Control) permissions directly in controller methods. This approach reduces boilerplate code and makes permission requirements explicit and easy to understand.

## Usage

### Basic Usage

```typescript
import { hasPermission } from '../decorators';

export class UserController {
  @hasPermission('users:read:own')
  static async getUserProfile(req: AuthRequest, res: Response) {
    // Method implementation
    res.json({ user: req.user });
  }
}
```

### Permission Format

The permission string follows the standard format used throughout the application:

```
resource:action:scope
```

Where:
- `resource`: The entity being accessed (e.g., `users`, `conversations`, `teams`)
- `action`: The operation being performed (e.g., `read`, `create`, `update`, `delete`)
- `scope`: The access level (`all`, `team`, or `own`)

### Route Setup

When using controllers with the `@hasPermission` decorator, you still need to include the `authMiddleware` in your routes, as the decorator relies on the authenticated user being available in the request object:

```typescript
import express from 'express';
import { authMiddleware } from '../middleware/auth.middleware';
import { UserController } from '../controllers/user.controller';

const router = express.Router();

router.get('/profile', 
  authMiddleware,
  UserController.getUserProfile
);

export default router;
```

## How It Works

The decorator:

1. Parses the permission string into resource, action, and scope components
2. Wraps the original controller method with permission checking logic
3. Checks if the authenticated user has the required permission
4. If the user has permission, executes the original method
5. If the user doesn't have permission, returns a 403 Forbidden response

## Advantages Over Middleware

While the application also provides the `checkPermission` middleware for routes, the decorator approach offers several advantages:

1. **Declarative Syntax**: Permission requirements are declared directly on the methods they protect
2. **Self-Documentation**: Makes it immediately clear what permissions a method requires
3. **Type Safety**: Leverages TypeScript's type system for better error checking
4. **Reduced Boilerplate**: Eliminates repetitive middleware chains in route definitions
5. **Maintainability**: Centralizes permission logic with the business logic it protects

## Example

Here's a complete example of a controller using the permission decorator:

```typescript
import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import { hasPermission } from '../decorators';

export class UserController {
  @hasPermission('users:read:all')
  static async getAllUsers(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'All users retrieved' });
  }

  @hasPermission('users:read:team')
  static async getTeamUsers(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'Team users retrieved' });
  }

  @hasPermission('users:read:own')
  static async getUserProfile(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'User profile retrieved' });
  }

  @hasPermission('users:update:own')
  static async updateUserProfile(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'User profile updated' });
  }
}
```

## Best Practices

1. **Don't be greedy**: Use the lowest permission scope required for each method
2. **Consistent Naming**: Follow the established naming conventions for resources and actions
3. **Documentation**: Include the required permission in method documentation
4. **Error Handling**: The decorator handles permission errors, but you may still need custom error handling for business logic
5. **Testing**: Test both the happy path (with permission) and error path (without permission)
