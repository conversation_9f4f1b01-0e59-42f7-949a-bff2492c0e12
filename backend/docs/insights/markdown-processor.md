# Markdown Insight Processor

The Markdown Insight Processor allows you to run custom prompts against conversation data and render the generated responses in Markdown format. This processor is especially useful for creating rich, formatted insights with customized analysis.

## Overview

The Markdown processor:
- Takes a custom prompt as input
- Runs the prompt against the entire conversation transcript
- Generates a response formatted in Markdown
- Renders the formatted content in the UI

## Usage

When creating a new insight using the Markdown processor:

1. Select "Markdown" from the insight type dropdown
2. Provide a descriptive name for your insight
3. Enter your custom prompt in the prompt field

## Custom Prompt Guidelines

Your custom prompt should be clear and specific about what analysis you want performed on the conversation. For example:

```
Create a detailed analysis of the customer objections in this conversation. 
For each objection:
1. Identify the specific objection raised
2. Analyze how effectively it was addressed
3. Suggest alternative approaches that could have been used

Format the response with clear headings, bullet points, and emphasized text where appropriate.
```

## Formatting Features

The Markdown renderer supports standard Markdown syntax:

- **Headings** (# H1, ## H2, etc.)
- **Emphasis** (*italic* and **bold**)
- **Lists** (ordered and unordered)
- **Links**
- **Blockquotes**
- **Code blocks**
- **Tables**

## Best Practices

- Keep prompts focused on a specific type of analysis
- Request structured output with specific sections
- Explicitly ask for formatting when needed (tables, lists, etc.)
- Consider creating multiple Markdown insights for different analyses rather than one complex insight

## Technical Details

The Markdown processor uses the high-quality model variant for the most sophisticated and detailed analysis. The output is rendered using React-Markdown in the frontend.
