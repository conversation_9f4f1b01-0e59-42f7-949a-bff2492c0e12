# Luno Backend RBAC Documentation

## Table of Contents

- [Overview](#overview)
- [RBAC Architecture](#rbac-architecture)
- [Permission Model](#permission-model)
  - [Permission Format](#permission-format)
  - [Scope Hierarchy](#scope-hierarchy)
  - [Permission Components](#permission-components)
- [Practical Examples](#practical-examples)
  - [Using Permission Helpers in Routes](#using-permission-helpers-in-routes)
  - [Using Permission Helpers in Controllers](#using-permission-helpers-in-controllers)
  - [Using the checkPermission Middleware](#using-the-checkpermission-middleware)
  - [Using the hasPermission Decorator](#using-the-haspermission-decorator)
  - [Hierarchical Permission Checking](#hierarchical-permission-checking)
- [Role Management](#role-management)
  - [System Roles](#system-roles)
  - [Custom Roles](#custom-roles)
  - [Role-Permission Assignment](#role-permission-assignment)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

Luno implements a comprehensive Role-Based Access Control (RBAC) system with hierarchical scopes. This system controls what actions users can perform on different resources based on their assigned roles and the permissions associated with those roles.

The RBAC system is built on three key components:
1. **Permissions**: Define what actions can be performed on which resources and at what scope
2. **Roles**: Collections of permissions that are assigned to users
3. **Middleware**: Enforces permission checks on API routes

## RBAC Architecture

The RBAC system follows a hierarchical scope model with three levels of access:

1. **Organization-wide access** (`all` scope)
2. **Team-level access** (`team` scope)
3. **Individual user access** (`own` scope)

This hierarchy is strictly enforced, where higher-level scopes automatically grant access to lower-level scopes. For example, a user with `all` scope for a resource can also access that resource at the `team` and `own` scopes.

```
┌─────────────┐
│             │
│  ALL Scope  │ ──────┐
│             │       │
└─────────────┘       │
       │              │
       ▼              │
┌─────────────┐       │
│             │       │
│ TEAM Scope  │ ◄─────┘
│             │       │
└─────────────┘       │
       │              │
       ▼              │
┌─────────────┐       │
│             │       │
│  OWN Scope  │ ◄─────┘
│             │
└─────────────┘
```

## Permission Model

### Permission Format

Permissions in Luno follow a standardized format:

```
resource:action:scope
```

Where:
- `resource`: The entity being accessed (e.g., `users`, `conversations`, `teams`)
- `action`: The operation being performed (e.g., `read`, `create`, `update`, `delete`)
- `scope`: The access level (`all`, `team`, or `own`)

Examples:
- `users:read:all` - Can read all users in the organization
- `conversations:update:team` - Can update conversations for the user's team
- `insights:create:own` - Can create insights for themselves

### Scope Hierarchy

The RBAC system implements a hierarchical scope model where higher scopes automatically grant access to lower scopes:

1. `all` scope grants access to `team` and `own` scopes
2. `team` scope grants access to `own` scope
3. `own` scope grants access only to the user's own resources

This means that:
- A user with `users:read:all` permission can access routes requiring `users:read:team` or `users:read:own`
- A user with `users:read:team` permission can access routes requiring `users:read:own`
- A user with `users:read:own` permission can only access routes requiring `users:read:own`

### Permission Components

The permission system is built on several components:

1. **Permission Entity**: Database model that stores permission definitions
   ```typescript
   // Example Permission entity
   {
     id: 1,
     name: 'users:read:all',
     resource: 'users',
     action: 'read',
     scope: 'all',
     description: 'Can read all users with all scope'
   }
   ```

2. **Permission Constants**: Predefined permission strings in `permissions.ts`
   ```typescript
   // Example from permissions.ts
   export const Permissions = {
     users: {
       read: {
         all: 'users:read:all',
         team: 'users:read:team',
         own: 'users:read:own'
       },
       // ...other actions
     },
     // ...other resources
   }
   ```

3. **Permission Helpers**: Utility functions for working with permissions
   ```typescript
   // Example helper functions
   getPermissionScopes(resource, action)
   hasPermissionDefinition(resource, action, scope)
   getPermissionString(resource, action, scope)
   ```

## Practical Examples

### Using Permission Helpers in Routes

```typescript
import express from 'express';
import { authMiddleware, checkPermission } from '../middleware/auth.middleware';

const router = express.Router();

// System operations - always require 'all' scope
router.get('/permissions', 
  authMiddleware,
  checkPermission('permissions', 'read', 'all'),
  PermissionController.findAll
);

// Organization operations
router.get('/users/organization', 
  authMiddleware,
  checkPermission('users', 'read', 'all'),
  UserController.getOrganizationUsers
);

// Team operations
router.get('/team/metrics', 
  authMiddleware,
  checkPermission('metrics', 'read', 'team'),
  MetricsController.getTeamMetrics
);

// Personal operations
router.get('/profile', 
  authMiddleware,
  checkPermission('users', 'read', 'own'),
  UserController.getUserProfile
);

export default router;
```

### Using Permission Helpers in Controllers

```typescript
import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import { getPermissionScopes } from '../lib/permissions';

export class PermissionController {
  // Get available scopes for a resource and action
  static async getAvailableScopes(req: AuthRequest, res: Response) {
    const { resource, action } = req.params;
    
    // Get all available scopes for this resource and action
    const scopes = getPermissionScopes(resource, action);
    
    if (scopes.length === 0) {
      return res.status(404).json({ message: `No permissions found` });
    }
    
    return res.json({ resource, action, scopes });
  }
}
```

### Using the checkPermission Middleware

The `checkPermission` middleware enforces permission checks on routes:

```typescript
// Example usage of checkPermission middleware
router.get('/users', 
  authMiddleware, 
  checkPermission('users', 'read', 'all'), 
  UserController.getAllUsers
);

router.post('/teams', 
  authMiddleware,
  checkPermission('teams', 'create', 'all'),
  TeamController.createTeam
);

router.put('/profile', 
  authMiddleware,
  checkPermission('users', 'update', 'own'),
  UserController.updateProfile
);
```

The middleware takes three parameters:
- `resource`: The resource being accessed (e.g., 'users', 'teams')
- `action`: The action being performed (e.g., 'read', 'create', 'update')
- `scope`: The required scope level ('all', 'team', or 'own')

If the user doesn't have the required permission, the middleware returns a 403 Forbidden response.

### Using the hasPermission Decorator

The `@hasPermission` decorator provides a more declarative way to enforce permissions directly in controller methods:

```typescript
import { hasPermission } from '../decorators';

export class UserController {
  @hasPermission('users:read:all')
  static async getAllUsers(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'All users retrieved successfully' });
  }

  @hasPermission('users:read:team')
  static async getTeamUsers(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'Team users retrieved successfully' });
  }

  @hasPermission('users:read:own')
  static async getUserProfile(req: AuthRequest, res: Response) {
    // Implementation
    res.json({ message: 'User profile retrieved successfully' });
  }
}
```

When using the decorator:
1. The permission string follows the same format: `resource:action:scope`
2. The decorator automatically checks if the user has the required permission
3. If the user doesn't have permission, it returns a 403 Forbidden response
4. If the user has permission, the original method is executed

Routes still need the `authMiddleware` to ensure the user is authenticated:

```typescript
router.get('/profile', 
  authMiddleware,
  UserController.getUserProfile  // Method with @hasPermission decorator
);
```

For more details on using the decorator, see the [Permission Decorator Documentation](./permission-decorator.md).

### Hierarchical Permission Checking

The RBAC system implements hierarchical scope checking:

```typescript
// Simplified example of hierarchical permission checking
async hasPermission(roleId: number, resource: string, action: string, requiredScope: 'all' | 'team' | 'own'): Promise<boolean> {
  const permissions = await this.getRolePermissions(roleId);
  const scopeHierarchy = { 
    all: ['all', 'team', 'own'], 
    team: ['team', 'own'], 
    own: ['own'] 
  };

  return permissions.some(permission => {
    if (permission.resource !== resource || permission.action !== action) {
      return false;
    }

    // Check if the permission's scope grants access to the required scope
    return scopeHierarchy[permission.scope].includes(requiredScope);
  });
}
```

## Role Management

### System Roles

Luno comes with predefined system roles that provide a starting point for access control:

1. **Admin**: Full access to all features and resources
   - Has all permissions with `all` scope
   - Can manage roles and permissions

2. **Team Manager**: Can manage team resources
   - Has team-level permissions for most resources
   - Can manage users within their team

3. **User**: Regular user access
   - Has own-level permissions for personal resources
   - Limited to managing their own data

These roles are defined in `system-roles.ts` and are automatically created for each organization during the seeding process.

### Custom Roles

Organizations can create custom roles with specific permission sets to meet their unique requirements:

1. **Creating Custom Roles**: Use the Role Service to create new roles
   ```typescript
   // Example of creating a custom role
   const role = await roleService.createRole({
     name: 'Custom Role',
     description: 'Custom role description',
     organizationId: organization.id
   });
   ```

2. **Assigning Permissions**: Use the Role Service to assign permissions to roles
   ```typescript
   // Example of assigning permissions to a role
   await roleService.updateRolePermissions(roleId, permissions);
   ```

### Role-Permission Assignment

The relationship between roles and permissions is managed through the `RolePermission` entity, which creates a many-to-many relationship:

```typescript
// Example role-permission relationship
{
  id: 1,
  roleId: 1,  // Admin role
  permissionId: 5  // users:read:all permission
}
```

This design allows for flexible permission assignments while maintaining data integrity.

## Best Practices

1. **Follow the Principle of Least Privilege**
   - Assign the minimum permissions necessary for users to perform their tasks
   - Start with the `User` role and add permissions as needed

2. **Use Scope Appropriately**
   - Use `own` scope for personal operations
   - Use `team` scope for team-level operations
   - Reserve `all` scope for organization-wide operations and system administrators

3. **Validate Permissions in Controllers**
   - Even with route middleware, validate permissions in controllers for nested resources
   - Use the `validateOrganizationAccess` helper to ensure organization context
   - Consider using the `@hasPermission` decorator for cleaner controller code

4. **Choose the Right Permission Enforcement Method**
   - Use the `checkPermission` middleware for simple route protection
   - Use the `@hasPermission` decorator for controller methods with complex logic
   - Combine both approaches as needed for comprehensive protection

5. **Keep System Roles Intact**
   - Avoid modifying the built-in system roles
   - Create custom roles instead of changing system roles

6. **Audit Permission Changes**
   - Log permission and role changes for security auditing
   - Regularly review role assignments

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check if the user has the required permission with the correct scope
   - Verify that the permission is correctly assigned to the user's role
   - Check if the permission exists in the database

2. **Missing Permissions**
   - Run the seed-roles script to ensure all permissions are created
   - Verify that the permission is defined in the `Permissions` object

3. **Role Hierarchy Issues**
   - Remember that scope follows a hierarchy: `all` > `team` > `own`
   - A user with a higher scope automatically has access to lower scopes

### Debugging Tips

1. **Check User Role**
   ```typescript
   // Example of checking a user's role
   const user = await userRepository.findOne({
     where: { id: userId },
     relations: ['role']
   });
   console.log('User role:', user.role.name);
   ```

2. **Check Role Permissions**
   ```typescript
   // Example of checking a role's permissions
   const permissions = await roleService.getRolePermissions(roleId);
   console.log('Role permissions:', permissions.map(p => p.name));
   ```

3. **Test Permission Check**
   ```typescript
   // Example of testing a permission check
   const hasPermission = await roleService.hasPermission(
     roleId, 
     'resource', 
     'action', 
     'scope'
   );
   console.log('Has permission:', hasPermission);
   ```
