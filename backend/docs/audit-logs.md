# Audit Logs

This document describes the audit logging system in Luno, which tracks user actions and system events for security, compliance, and debugging purposes.

## Overview

The audit logging system captures detailed information about user actions and system events across the application. It provides a chronological record of activities that can be used for:

- Security monitoring and threat detection
- Compliance with regulatory requirements
- Debugging and troubleshooting
- User activity analysis

## Data Model

The `AuditLog` entity stores the following information:

| Field         | Type                | Description                                           |
|---------------|---------------------|-------------------------------------------------------|
| id            | number              | Unique identifier for the audit log entry             |
| userId        | number              | ID of the user who performed the action (nullable)    |
| organizationId| number              | ID of the organization where the action occurred      |
| action        | string              | Type of action performed (e.g., create, update, delete)|
| resource      | string              | Resource type affected (e.g., user, conversation)     |
| entityId      | string              | ID of the specific entity affected (nullable)         |
| ipAddress     | string              | IP address of the request (nullable)                  |
| userAgent     | string              | User agent of the request (nullable)                  |
| requestData   | object              | Request data associated with the action (nullable)    |
| responseData  | object              | Response data associated with the action (nullable)   |
| changes       | object              | Changes made to the entity (nullable)                 |
| metadata      | object              | Additional contextual information (nullable)          |
| status        | success/error       | Outcome of the action                                 |
| errorMessage  | string              | Error message if the action failed (nullable)         |
| createdAt     | Date                | Timestamp when the audit log was created              |
| user          | User                | Relation to the User entity                           |

## API Endpoints

### Find All Audit Logs

```
GET /api/audit
```

**Permission Required:** `audit:read:all`

**Query Parameters:**
- `organizationId`: Filter by organization ID
- `userId`: Filter by user ID
- `resource`: Filter by resource type
- `action`: Filter by action type
- `entityId`: Filter by entity ID
- `startDate`: Filter by start date
- `endDate`: Filter by end date
- `page`: Page number for pagination (default: 1)
- `limit`: Number of items per page (default: 20)

**Response:**
```json
{
  "logs": [
    {
      "id": 1,
      "action": "create",
      "resource": "user",
      "entityId": "123",
      "ipAddress": "***********",
      "changes": { "name": { "old": null, "new": "John Doe" } },
      "metadata": { "source": "web" },
      "status": "success",
      "createdAt": "2025-03-19T07:55:05.000Z",
      "user": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
      }
    }
  ],
  "total": 100
}
```

### Find Audit Logs for a Specific Entity

```
GET /api/audit/:resource/:entityId
```

**Permission Required:** `audit:read:all`

**Path Parameters:**
- `resource`: Resource type (e.g., user, conversation)
- `entityId`: ID of the entity

**Query Parameters:**
- `page`: Page number for pagination (default: 1)
- `limit`: Number of items per page (default: 20)

**Response:**
```json
{
  "logs": [
    {
      "id": 1,
      "action": "create",
      "resource": "user",
      "entityId": "123",
      "ipAddress": "***********",
      "changes": { "name": { "old": null, "new": "John Doe" } },
      "metadata": { "source": "web" },
      "status": "success",
      "createdAt": "2025-03-19T07:55:05.000Z",
      "user": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
      }
    }
  ],
  "total": 5
}
```

## Usage in Code

### The `@audited` Decorator

The easiest way to add audit logging to a controller method is to use the `@audited` decorator:

```typescript
import { audited } from '../decorators/audit.decorator';

@audited({
  action: 'create',
  resource: 'user',
  includeRequest: true,
  sensitiveFields: ['password', 'confirmPassword']
})
async createUser(req: AuthRequest, res: Response) {
  // Method implementation
}
```

The decorator automatically:
- Captures the state of the entity before and after the operation
- Records request and response data (if configured)
- Tracks changes made to the entity
- Handles error cases
- Creates an audit log entry with all relevant information

#### Configuration Options

The `@audited` decorator accepts the following options:

| Option           | Type                  | Description                                         | Default |
|------------------|------------------------|-----------------------------------------------------|---------|
| action           | string                 | Type of action being performed (required)           | -       |
| resource         | string                 | Resource type being affected (required)             | -       |
| entityIdPath     | string                 | Path to the entity ID in the request                | req.params.id |
| includeRequest   | boolean                | Whether to include request data in the log          | false   |
| includeResponse  | boolean                | Whether to include response data in the log         | false   |
| capturePreState  | boolean                | Whether to capture entity state before operation    | true    |
| capturePostState | boolean                | Whether to capture entity state after operation     | true    |
| sensitiveFields  | string[]               | Fields to redact from request/response data         | []      |
| metadata         | object or function     | Additional context to include in the log            | undefined |
| auditOnError     | boolean                | Whether to create audit logs for failed operations  | false   |

#### Examples

**Basic Usage:**
```typescript
@audited({
  action: 'create',
  resource: 'user'
})
```

**Including Request and Response Data:**
```typescript
@audited({
  action: 'update',
  resource: 'project',
  includeRequest: true,
  includeResponse: true
})
```

**Redacting Sensitive Information:**
```typescript
@audited({
  action: 'create',
  resource: 'user',
  includeRequest: true,
  sensitiveFields: ['password', 'ssn', 'creditCard']
})
```

**Adding Custom Metadata:**
```typescript
@audited({
  action: 'delete',
  resource: 'conversation',
  metadata: { source: 'admin-panel' }
})
```

**Dynamic Metadata:**
```typescript
@audited({
  action: 'update',
  resource: 'project',
  metadata: (req, responseBody) => ({
    teamId: req.user.teamId,
    projectType: responseBody?.type
  })
})
```

### Manual Audit Logging (Advanced Use Cases)

While the `@audited` decorator is the preferred approach for most scenarios, there may be cases where you need more control over the audit logging process. In these situations, you can use the `AuditService` directly:

```typescript
import { AuditService } from '../services/audit.service';

// Inject the service
constructor(private auditService: AuditService) {}

// Create an audit log
await this.auditService.createAuditLog({
  userId: req.user.id,
  organizationId: req.user.organizationId,
  action: 'create',
  resource: 'user',
  entityId: user.id.toString(),
  ipAddress: req.ip,
  userAgent: req.headers['user-agent'],
  changes: {
    name: { old: null, new: user.name },
    email: { old: null, new: user.email }
  },
  status: 'success'
});
```

Use this approach only when:
- You need to create audit logs outside of the request-response cycle
- You're implementing custom logic that the decorator doesn't support
- You're working with background jobs or scheduled tasks
- You need to create multiple audit logs within a single operation

### Tracking Entity Changes

The `audit.utils.ts` file provides utility functions to help track entity changes:

```typescript
import { getEntityId, trackChanges } from '../utils/audit.utils';

// Get entity ID (handles different ID types)
const entityId = getEntityId(entity);

// Track changes between old and new entity
const changes = trackChanges(oldEntity, newEntity);
```

## RBAC Integration

The audit log system integrates with the Role-Based Access Control (RBAC) system using the `@hasPermission` decorator:

```typescript
import { hasPermission } from '../decorators/permission.decorator';

@hasPermission('audit:read:all')
async findAll(req: Request, res: Response) {
  // Method implementation
}
```

This ensures that only users with the appropriate permissions can access audit logs.

## Best Practices

1. **Use the `@audited` decorator**: For most controller methods, the decorator provides a simple and consistent way to add audit logging.

2. **Be selective about data capture**: Only include request and response data when necessary, and always specify sensitive fields to redact.

3. **Always log sensitive operations**: Create audit logs for all operations that create, update, or delete data.

4. **Include relevant context**: Use the `metadata` field to include additional context that might be useful for debugging or analysis.

5. **Track changes**: When updating entities, ensure that changes are properly captured.

6. **Handle errors**: Configure `auditOnError: true` for critical operations to ensure failures are also logged.

7. **Respect privacy**: Be careful not to log sensitive personal information or credentials.

## Future Improvements

Potential future improvements to the audit logging system:

1. **Export functionality**: Allow exporting audit logs to CSV or PDF for compliance reporting.

2. **Advanced filtering**: Add more advanced filtering options for better search capabilities.

3. **Visualization**: Create dashboards to visualize audit log data and identify patterns.

4. **Retention policies**: Implement policies for log retention and archiving.
