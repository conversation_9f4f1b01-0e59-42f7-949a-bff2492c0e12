# Luno Backend Authentication Documentation

## Table of Contents

- [Overview](#overview)
- [Authentication Architecture](#authentication-architecture)
- [Authentication Flows](#authentication-flows)
  - [Registration Flow](#registration-flow)
  - [Login Flow](#login-flow)
  - [Token Validation](#token-validation)
  - [Socket Authentication](#socket-authentication)
- [Authentication Middleware](#authentication-middleware)
- [API Reference](#api-reference)

## Overview

Luno's authentication system is built on JSON Web Tokens (JWT) with support for two-factor authentication. The system integrates with a Role-Based Access Control (RBAC) mechanism that implements a hierarchical scope system.

This document covers the core authentication functionality, including user registration, login, token validation, and middleware integration.

## Authentication Architecture

Luno's authentication architecture follows these principles:

1. **JWT-based Authentication**: All authentication is handled via JSON Web Tokens
2. **Middleware-based Protection**: Express middleware validates tokens and permissions
3. **Role-Based Access Control**: Users have roles with specific permissions
4. **Two-Factor Authentication**: Optional TOTP-based two-factor authentication
5. **Socket.IO Authentication**: WebSocket connections are authenticated using the same token system

The authentication flow is as follows:

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │      │             │
│    Client   │─────▶│  Auth API   │─────▶│  JWT Token  │─────▶│ Protected   │
│             │      │             │      │             │      │ Resources   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                                                              │
       │                                                              │
       └──────────────────────────────────────────────────────────────┘
                                 Authenticated
                                   Requests
```

## Authentication Flows

### Registration Flow

The registration process creates both a new user and a new organization in a single transaction.

#### Process

1. Client submits registration data (email, password, name, organizationName)
2. Server validates the data
3. Server checks if the user already exists
4. Server creates a new organization
5. Server creates a new user with a hashed password
6. Server generates a JWT token
7. Server returns the token and user data

#### Example Usage

```typescript
// Example of how to hash a password
const salt = await bcrypt.genSalt(10);
const hashedPassword = await bcrypt.hash(password, salt);

// Example of how to generate a JWT token
const token = jwt.sign(
  { id: userId, email: userEmail },
  process.env.JWT_SECRET || 'your-secret-key',
  { expiresIn: '24h' }
);
```

#### Security Considerations

- Passwords are hashed using bcrypt with a salt
- Registration is performed in a transaction to ensure data consistency
- Sensitive data (password) is never returned to the client

### Login Flow

The login process authenticates a user and optionally validates a two-factor authentication token.

#### Process

1. Client submits login credentials (email, password, optional TOTP token)
2. Server validates the credentials
3. If 2FA is enabled for the user:
   - If no TOTP token is provided, server returns a 403 status with a 2FA required message
   - If a TOTP token is provided, server validates it
4. Server generates a JWT token
5. Server returns the token and user data

#### Example Usage

```typescript
// Example of how to verify a password
const validPassword = await bcrypt.compare(password, user.password);

// Example of how to verify a TOTP token
const twoFactorService = new TwoFactorService();
const validToken = twoFactorService.verifyToken(totpToken, user.twoFactorSecret);

// Example of removing sensitive data from response
const { password: _, twoFactorSecret: __, ...userWithoutSensitive } = user;
```

#### Security Considerations

- Generic error messages are used to prevent user enumeration
- Passwords are compared using bcrypt's secure comparison
- Two-factor authentication adds an additional layer of security
- Sensitive data (password, 2FA secret) is never returned to the client

### Token Validation

JWT tokens are validated in the authentication middleware for both HTTP requests and WebSocket connections.

#### Process

1. Client includes the JWT token in the Authorization header
2. Server extracts the token from the header
3. Server verifies the token using the JWT secret
4. Server retrieves the user from the database
5. Server attaches the user to the request object

#### Example Usage

```typescript
// Example of how to verify a JWT token
const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
const userId = (decoded as any).id;

// Example of how to retrieve a user with relations
const user = await userRepository.findOne({ 
  where: { id: userId },
  relations: ['organization', 'team', 'role']
});
```

#### Security Considerations

- Tokens are verified using the JWT secret
- Token expiration is enforced
- User is retrieved from the database to ensure they still exist and have access

### Socket Authentication

WebSocket connections are authenticated using the same JWT token system.

#### Process

1. Client includes the JWT token in the socket handshake
2. Server extracts the token from the handshake
3. Server verifies the token using the JWT secret
4. Server retrieves the user from the database
5. Server attaches the user to the socket data

#### Example Usage

```typescript
// Example of how to extract a token from socket handshake
const token = socket.handshake.auth.token || 
             socket.handshake.headers.authorization?.replace('Bearer ', '');

// Example of how to attach user data to socket
socket.data.user = user;
```

## Authentication Middleware

The authentication middleware is used to protect routes that require authentication.

### HTTP Middleware

The `authenticateOrganization` function is used to protect HTTP routes:

```typescript
// Example of authentication middleware
export const authenticateOrganization: RequestHandler = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'No authentication token provided' });
    }
    
    // Verify token and get user
    const user = await authenticateUser(token);
    
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }
    
    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Please authenticate' });
  }
};
```

### Usage Example

```typescript
// Protect a route with authentication
router.get('/profile', authMiddleware, UserController.getProfile);

// Protect a route with authentication and permission check
router.get('/users', authMiddleware, checkPermission('users', 'read', 'all'), UserController.getAllUsers);
```

## API Reference

### Authentication Endpoints

#### POST /auth/register

Registers a new user and organization.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "name": "John Doe",
  "organizationName": "Example Organization"
}
```

**Response (201 Created):**
```json
{
  "message": "User registered successfully",
  "token": "jwt-token",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "organization": {
      "id": 1,
      "name": "Example Organization"
    }
  }
}
```

#### POST /auth/login

Authenticates a user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "totpToken": "123456"  // Optional, required if 2FA is enabled
}
```

**Response (200 OK):**
```json
{
  "token": "jwt-token",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "twoFactorEnabled": true,
    "organization": {
      "id": 1,
      "name": "Example Organization"
    },
    "team": {
      "id": 1,
      "name": "Example Team"
    }
  }
}
```

**Response (403 Forbidden) - 2FA Required:**
```json
{
  "message": "2FA required",
  "require2FA": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "twoFactorEnabled": true
  }
}
```

#### GET /auth/me

Gets the current authenticated user.

**Headers:**
```
Authorization: Bearer jwt-token
```

**Response (200 OK):**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "twoFactorEnabled": true,
    "organization": {
      "id": 1,
      "name": "Example Organization"
    },
    "team": {
      "id": 1,
      "name": "Example Team"
    }
  }
}
