# API Routes

This document lists all available API routes in the application.

Last updated: 2025-04-03T19:16:55.391Z

Total routes: 88

| Method | Path | Handler | File |
| ------ | ---- | ------- | ---- |
| GET | /api/automation/node-types/categories | getAllNodeCategories | src/routes/node-types.routes.ts |
| GET | /api/automation/node-types/types | getAllNodeTypes | src/routes/node-types.routes.ts |
| GET | /api/automation/node-types/types/:id | getNodeTypeById | src/routes/node-types.routes.ts |
| GET | /api/automation/node-types/types/category/:category | getNodeTypesByCategory | src/routes/node-types.routes.ts |
| GET | /api/automations | unknown | src/routes/automation.routes.ts |
| POST | /api/automations | unknown | src/routes/automation.routes.ts |
| GET | /api/automations/:id | unknown | src/routes/automation.routes.ts |
| PUT | /api/automations/:id | unknown | src/routes/automation.routes.ts |
| DELETE | /api/automations/:id | unknown | src/routes/automation.routes.ts |
| GET | /api/automations/count | unknown | src/routes/automation.routes.ts |
| GET | /api/conversation-sources | unknown | src/routes/conversation-sources.ts |
| POST | /api/conversation-sources | unknown | src/routes/conversation-sources.ts |
| PUT | /api/conversation-sources/:id | unknown | src/routes/conversation-sources.ts |
| GET | /api/conversation-sources/:id | unknown | src/routes/conversation-sources.ts |
| POST | /api/conversation-sources/:id/oauth/start | unknown | src/routes/conversation-sources.ts |
| PUT | /api/conversation-sources/:id/settings | unknown | src/routes/conversation-sources.ts |
| POST | /api/conversation-sources/:id/webhooks | unknown | src/routes/conversation-sources.ts |
| PUT | /api/conversation-sources/:id/webhooks/invalidate | unknown | src/routes/conversation-sources.ts |
| PUT | /api/conversation-sources/:id/webhooks/regenerate | unknown | src/routes/conversation-sources.ts |
| PUT | /api/conversation-sources/:id/webhooks/secret | unknown | src/routes/conversation-sources.ts |
| GET | /api/conversation-sources/adapters | unknown | src/routes/conversation-sources.ts |
| GET | /api/conversation-sources/adapters/:id | unknown | src/routes/conversation-sources.ts |
| GET | /api/conversations/stats | ConversationStatsController.getConversationStats | src/routes/conversation.stats.routes.ts |
| GET | /api/conversations/stats/medium | ConversationStatsController.getConversationMediumStats | src/routes/conversation.stats.routes.ts |
| GET | /api/copilot-threads | unknown | src/routes/copilot-thread.ts |
| POST | /api/copilot-threads | unknown | src/routes/copilot-thread.ts |
| GET | /api/copilot-threads/:id | unknown | src/routes/copilot-thread.ts |
| PUT | /api/copilot-threads/:id | unknown | src/routes/copilot-thread.ts |
| DELETE | /api/copilot-threads/:id | unknown | src/routes/copilot-thread.ts |
| GET | /api/copilot/:threadId | CopilotController.getThreadMessages | src/routes/copilot.ts |
| GET | /api/insights/insight-definitions | unknown | src/routes/insight.routes.ts |
| POST | /api/insights/insight-definitions | createInsightDefinition | src/routes/insight.routes.ts |
| PUT | /api/insights/insight-definitions/:id | updateInsightDefinition | src/routes/insight.routes.ts |
| DELETE | /api/insights/insight-definitions/:id | deleteInsightDefinition | src/routes/insight.routes.ts |
| GET | /api/insights/insight-definitions/:id/competitor-summary | getCompetitorSummary | src/routes/insight.routes.ts |
| GET | /api/insights/insight-definitions/:id/insights | unknown | src/routes/insight.routes.ts |
| POST | /api/insights/organizations/:organizationId/insights | createInsightDefinition | src/routes/insight.routes.ts |
| PUT | /api/insights/organizations/:organizationId/insights/:id | updateInsightDefinition | src/routes/insight.routes.ts |
| DELETE | /api/insights/organizations/:organizationId/insights/:id | deleteInsightDefinition | src/routes/insight.routes.ts |
| GET | /api/insights/organizations/:organizationId/insights/:insightId | unknown | src/routes/insight.routes.ts |
| GET | /api/insights/organizations/:organizationId/insights/count | unknown | src/routes/insight.routes.ts |
| GET | /api/insights/organizations/:organizationId/insights/types | unknown | src/routes/insight.routes.ts |
| GET | /api/invoices/:id | res | src/routes/invoices.routes.ts |
| GET | /api/invoices/:id/download | res | src/routes/invoices.routes.ts |
| GET | /api/invoices/organization/:organizationId | res | src/routes/invoices.routes.ts |
| GET | /api/invoices/organization/:organizationId/metrics | res | src/routes/invoices.routes.ts |
| GET | /api/organizations/:id/sso-settings | unknown | src/routes/organization.routes.ts |
| PATCH | /api/organizations/:id/sso-settings | unknown | src/routes/organization.routes.ts |
| DELETE | /api/paymentmethods/:id | res | src/routes/payment-methods.routes.ts |
| POST | /api/paymentmethods/attach | res | src/routes/payment-methods.routes.ts |
| POST | /api/paymentmethods/default | res | src/routes/payment-methods.routes.ts |
| GET | /api/paymentmethods/organization/:organizationId | res | src/routes/payment-methods.routes.ts |
| POST | /api/paymentmethods/setup-intent | res | src/routes/payment-methods.routes.ts |
| GET | /api/plans | res | src/routes/plans.routes.ts |
| GET | /api/plans/:id | res | src/routes/plans.routes.ts |
| POST | /api/prompts/improve-prompt | res | src/routes/prompt.routes.ts |
| GET | /api/roles | res | src/routes/role.routes.ts |
| POST | /api/roles | res | src/routes/role.routes.ts |
| GET | /api/roles/:id | res | src/routes/role.routes.ts |
| PUT | /api/roles/:id | res | src/routes/role.routes.ts |
| DELETE | /api/roles/:id | res | src/routes/role.routes.ts |
| GET | /api/sessions | unknown | src/routes/sessions.routes.ts |
| DELETE | /api/sessions/:sessionId | unknown | src/routes/sessions.routes.ts |
| POST | /api/sessions/end-all-except-current | unknown | src/routes/sessions.routes.ts |
| GET | /api/stats/conversations | StatsController.getConversationStats | src/routes/stats.routes.ts |
| POST | /api/stripewebhook/stripe | res | src/routes/stripe-webhook.routes.ts |
| POST | /api/subscriptions | res | src/routes/subscriptions.routes.ts |
| GET | /api/subscriptions/:id | res | src/routes/subscriptions.routes.ts |
| PUT | /api/subscriptions/:id/cancel | res | src/routes/subscriptions.routes.ts |
| PUT | /api/subscriptions/:id/plan | res | src/routes/subscriptions.routes.ts |
| PUT | /api/subscriptions/:id/reactivate | res | src/routes/subscriptions.routes.ts |
| PUT | /api/subscriptions/:id/seats | res | src/routes/subscriptions.routes.ts |
| GET | /api/subscriptions/organization/:organizationId | res | src/routes/subscriptions.routes.ts |
| GET | /api/subscriptions/organization/:organizationId/active | res | src/routes/subscriptions.routes.ts |
| GET | /api/subscriptions/organization/:organizationId/metrics | res | src/routes/subscriptions.routes.ts |
| GET | /api/teams | unknown | src/routes/team.routes.ts |
| POST | /api/teams | unknown | src/routes/team.routes.ts |
| GET | /api/teams/:teamId | unknown | src/routes/team.routes.ts |
| PUT | /api/teams/:teamId | unknown | src/routes/team.routes.ts |
| DELETE | /api/teams/:teamId | unknown | src/routes/team.routes.ts |
| POST | /api/teams/:teamId/users | unknown | src/routes/team.routes.ts |
| GET | /api/usage/organization/:organizationId | res | src/routes/usage.routes.ts |
| GET | /api/usage/organization/:organizationId/limits | res | src/routes/usage.routes.ts |
| POST | /api/users | createUser | src/routes/user.routes.ts |
| PUT | /api/users/:id/role | assignRoleToUser | src/routes/user.routes.ts |
| GET | /api/users/organization | getOrganizationUsers | src/routes/user.routes.ts |
| GET | /api/userseatallocation/organization/:organizationId | res | src/routes/user-seat-allocation.routes.ts |
| POST | /api/webhooks/:source/:webhookId | unknown | src/routes/webhooks.ts |
