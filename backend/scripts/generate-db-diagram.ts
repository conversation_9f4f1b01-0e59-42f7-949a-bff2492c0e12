#!/usr/bin/env ts-node

import { createDataSource } from '../src/data-source';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Generate a database diagram using Mermaid class notation
 * This creates a visual representation of the database schema
 */
async function generateDatabaseDiagram() {
  const dataSource = createDataSource();
  
  try {
    await dataSource.initialize();
    console.log('Database connection established');

    // Query to get all tables
    const tables = await dataSource.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    // Start building the Mermaid diagram using class notation
    let mermaidDiagram = '```mermaid\nclassDiagram\n';
    
    // Process each table
    for (const tableRow of tables) {
      const tableName = tableRow.table_name;
      
      // Query for table columns
      const columns = await dataSource.query(`
        SELECT 
          column_name, 
          data_type, 
          is_nullable,
          column_default,
          (
            SELECT COUNT(*) 
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            WHERE tc.table_name = c.table_name 
            AND kcu.column_name = c.column_name 
            AND tc.constraint_type = 'PRIMARY KEY'
          ) as is_primary_key
        FROM information_schema.columns c
        WHERE table_name = $1
        ORDER BY ordinal_position;
      `, [tableName]);
      
      // Add class definition
      mermaidDiagram += `  class ${tableName} {\n`;
      
      // Add columns to the class definition
      for (const column of columns) {
        const dataType = column.data_type;
        const columnName = column.column_name;
        const isNullable = column.is_nullable === 'YES' ? '?' : '';
        const isPrimaryKey = parseInt(column.is_primary_key) > 0 ? ' (PK)' : '';
        
        mermaidDiagram += `    ${dataType} ${columnName}${isNullable}${isPrimaryKey}\n`;
      }
      
      mermaidDiagram += `  }\n`;
    }
    
    // Query for foreign key relationships
    const relationships = await dataSource.query(`
      SELECT
        tc.table_name AS source_table,
        ccu.table_name AS target_table,
        kcu.column_name AS source_column,
        ccu.column_name AS target_column
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
      ORDER BY tc.table_name;
    `);
    
    // Add relationships
    for (const rel of relationships) {
      mermaidDiagram += `  ${rel.source_table} --|> ${rel.target_table} : ${rel.source_column}\n`;
    }
    
    // Finalize diagram
    mermaidDiagram += '```';
    
    // Write to file
    const outputPath = path.join(__dirname, '..', 'docs', 'database-diagram.md');
    
    // Create the docs directory if it doesn't exist
    const docsPath = path.join(__dirname, '..', 'docs');
    if (!fs.existsSync(docsPath)) {
      fs.mkdirSync(docsPath, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, 
      `# Database Schema Diagram\n\nThis diagram is automatically generated from the database schema using class notation. Do not edit manually.\n\nLast updated: ${new Date().toISOString()}\n\n${mermaidDiagram}`
    );
    
    console.log(`Database diagram generated at ${outputPath}`);
    
    return outputPath;
  } catch (error) {
    console.error('Error generating database diagram:', error);
    throw error;
  } finally {
    // Close the database connection
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the script if it's called directly
if (require.main === module) {
  generateDatabaseDiagram()
    .then((outputPath) => {
      console.log(`Database diagram generated successfully at ${outputPath}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Failed to generate database diagram:', error);
      process.exit(1);
    });
}

export { generateDatabaseDiagram };
