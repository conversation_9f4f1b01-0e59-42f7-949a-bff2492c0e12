#!/usr/bin/env node
/**
 * <PERSON>ript to reprocess insights for specified conversations
 * 
 * Usage:
 *   ts-node scripts/reprocess-insights.ts --id=123
 *   ts-node scripts/reprocess-insights.ts --from=100 --to=200
 *   ts-node scripts/reprocess-insights.ts --project=456
 *   ts-node scripts/reprocess-insights.ts --list (interactive mode)
 * 
 * This script will:
 * 1. Delete all existing insights for the specified conversation(s)
 * 2. Queue them for reprocessing
 */

// Load environment variables from .env file
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from the .env file in the backend directory
dotenv.config({ path: path.resolve(__dirname, '../.env') });

import { AppDataSource } from '../src/data-source';
import { Conversation } from '../src/models/conversation';
import { Insight } from '../src/models/insight';
import { insightQueue } from '../src/queues';
import { ConversationState } from '../src/models/conversation-state';
import * as readline from 'readline';
import { Project } from '../src/models/project';
import { Team } from '../src/models/team';
import { InsightDefinition } from '../src/models/insight-definition';

// Create interface for command line argument parsing
interface CommandArgs {
  id?: number;
  from?: number;
  to?: number;
  project?: number;
  team?: number;
  list?: boolean;
  help?: boolean;
}

/**
 * Parse command line arguments
 */
function parseArgs(): CommandArgs {
  const args: CommandArgs = {};
  
  for (const arg of process.argv.slice(2)) {
    // Match formats: --key=value, --key:value, and standalone --key
    const keyValueMatch = arg.match(/^--([a-zA-Z]+)(?:[=:](.+))?$/);
    
    if (keyValueMatch) {
      const [, key, value] = keyValueMatch;
      
      if (key === 'list' || key === 'help') {
        args[key] = true;
      } else if (value !== undefined) {
        args[key] = Number(value);
      } else {
        // Handle --key format without value (we'll look for the next arg)
        const nextArgIndex = process.argv.indexOf(arg) + 1;
        if (nextArgIndex < process.argv.length) {
          const nextArg = process.argv[nextArgIndex];
          if (!nextArg.startsWith('--')) {
            args[key] = Number(nextArg);
          }
        }
      }
    }
  }
  
  return args;
}

/**
 * Display help message
 */
function showHelp(): void {
  console.log(`
Reprocess Insights Script
=========================

This script allows you to delete and requeue insights for specified conversations.

Options:
  --id=NUMBER       Reprocess insights for a specific conversation ID
  --from=NUMBER     Start of conversation ID range
  --to=NUMBER       End of conversation ID range
  --project=NUMBER  Reprocess all conversations in a project
  --team=NUMBER     Reprocess all conversations in a team
  --list            Interactive mode - list recent conversations to choose from
  --help            Show this help message

Examples:
  ts-node scripts/reprocess-insights.ts --id=123
  ts-node scripts/reprocess-insights.ts --from=100 --to=200
  ts-node scripts/reprocess-insights.ts --project=456
  ts-node scripts/reprocess-insights.ts --list
  `);
}

/**
 * Reprocess insights for a single conversation
 */
async function reprocessConversationInsights(conversationId: number): Promise<void> {
  console.log(`Processing conversation ${conversationId}...`);
  
  // Find conversation with related team data
  const conversation = await AppDataSource.getRepository(Conversation)
    .createQueryBuilder('conversation')
    .leftJoinAndSelect('conversation.team', 'team')
    .leftJoinAndSelect('team.projects', 'projects')
    .leftJoinAndSelect('projects.insightDefinitions', 'insightDefinitions')
    .where('conversation.id = :id', { id: conversationId })
    .getOne();
  
  if (!conversation) {
    console.error(`Conversation ${conversationId} not found`);
    return;
  }

  // Get organization ID from team
  const organizationId = conversation.team?.organizationId;
  if (!organizationId) {
    console.error(`No organization found for conversation ${conversationId}`);
    return;
  }
  
  // Delete existing insights
  const deleteResult = await AppDataSource.getRepository(Insight).delete({ conversationId });
  console.log(`Deleted ${deleteResult.affected || 0} existing insights for conversation ${conversationId}`);
  
  // Update conversation status to processing insights
  await conversation.transitionTo(ConversationState.PROCESSING_INSIGHTS, {
    reason: 'Reprocessing insights via script'
  });
  
  // Count insights queued
  let insightsQueued = 0;
  
  // Process insights for all projects associated with the team
  for (const project of conversation.team.projects) {
    // Queue each insight definition for processing
    for (const definition of project.insightDefinitions) {
      await insightQueue.add('process-insight', {
        conversationId: conversation.id,
        insightDefinitionId: definition.id,
        organizationId
      });
      insightsQueued++;
    }
  }
  
  console.log(`Queued ${insightsQueued} insights for conversation ${conversationId}`);
}

/**
 * Reprocess insights for multiple conversations
 */
async function reprocessMultipleConversations(conversationIds: number[]): Promise<void> {
  console.log(`Reprocessing insights for ${conversationIds.length} conversations...`);
  
  let processed = 0;
  let skipped = 0;
  
  for (const id of conversationIds) {
    try {
      await reprocessConversationInsights(id);
      processed++;
    } catch (error) {
      console.error(`Error processing conversation ${id}:`, error);
      skipped++;
    }
  }
  
  console.log(`Processed ${processed} conversations, skipped ${skipped} due to errors`);
}

/**
 * Get recent conversations to choose from
 */
async function getRecentConversations(limit = 20): Promise<Conversation[]> {
  return AppDataSource.getRepository(Conversation)
    .createQueryBuilder('conversation')
    .leftJoinAndSelect('conversation.team', 'team')
    .select([
      'conversation.id',
      'conversation.externalId',
      'conversation.processingStatus',
      'conversation.created_at',
      'team.name'
    ])
    .orderBy('conversation.created_at', 'DESC')
    .limit(limit)
    .getMany();
}

/**
 * Show interactive selection menu
 */
async function showInteractiveMenu(): Promise<void> {
  console.log('Loading recent conversations...');
  
  const conversations = await getRecentConversations();
  
  if (conversations.length === 0) {
    console.log('No conversations found');
    return;
  }
  
  // Display conversations
  console.log('\nRecent conversations:');
  console.log('--------------------');
  
  conversations.forEach((conv, index) => {
    const createdAt = new Date(conv.created_at).toLocaleString();
    // Use externalId or conversation id as identifier
    const identifier = conv.externalId || `Conversation ${conv.id}`;
    console.log(`${index + 1}. [ID: ${conv.id}] ${identifier} (${createdAt}) - Team: ${conv.team?.name || 'Unknown'}`);
  });
  
  // Create readline interface
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  // Prompt for selection
  rl.question('\nEnter conversation number(s) to reprocess (e.g., "1" or "1,3,5" or "1-5"): ', async (answer) => {
    rl.close();
    
    const selectedIds: number[] = [];
    
    // Parse input for individual numbers, comma-separated values, and ranges
    const parts = answer.split(',').map(part => part.trim());
    
    for (const part of parts) {
      if (part.includes('-')) {
        // Handle range (e.g., "1-5")
        const [start, end] = part.split('-').map(Number);
        
        if (!isNaN(start) && !isNaN(end) && start <= end) {
          for (let i = start; i <= end; i++) {
            if (i > 0 && i <= conversations.length) {
              selectedIds.push(conversations[i - 1].id);
            }
          }
        }
      } else {
        // Handle single number
        const index = Number(part);
        
        if (!isNaN(index) && index > 0 && index <= conversations.length) {
          selectedIds.push(conversations[index - 1].id);
        }
      }
    }
    
    if (selectedIds.length === 0) {
      console.log('No valid selections made');
      return;
    }
    
    console.log(`Selected conversation IDs: ${selectedIds.join(', ')}`);
    
    try {
      await reprocessMultipleConversations(selectedIds);
      console.log('Processing complete');
    } catch (error) {
      console.error('Error during processing:', error);
    } finally {
      // Ensure clean exit
      process.exit(0);
    }
  });
}

/**
 * Get all conversation IDs for a project
 */
async function getConversationIdsByProject(projectId: number): Promise<number[]> {
  // Get project with its associated teams
  const project = await AppDataSource.getRepository(Project)
    .createQueryBuilder('project')
    .leftJoinAndSelect('project.teams', 'teams')
    .where('project.id = :id', { id: projectId })
    .getOne();
  
  if (!project || !project.teams || project.teams.length === 0) {
    return [];
  }
  
  // Get all conversations from all teams associated with this project
  const teamIds = project.teams.map(team => team.id);
  
  const conversations = await AppDataSource.getRepository(Conversation)
    .createQueryBuilder('conversation')
    .where('conversation.team_id IN (:...teamIds)', { teamIds })
    .select('conversation.id')
    .getMany();
  
  return conversations.map(conv => conv.id);
}

/**
 * Get all conversation IDs for a team
 */
async function getConversationIdsByTeam(teamId: number): Promise<number[]> {
  const team = await AppDataSource.getRepository(Team)
    .createQueryBuilder('team')
    .leftJoinAndSelect('team.conversations', 'conversations')
    .where('team.id = :id', { id: teamId })
    .getOne();
  
  if (!team || !team.conversations) {
    return [];
  }
  
  return team.conversations.map(conv => conv.id);
}

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    // Parse command line arguments
    const args = parseArgs();
    
    // Show help if requested
    if (args.help) {
      showHelp();
      return;
    }
    
    // Check for correct format of from/to parameters
    if ((args.from !== undefined && args.to === undefined) || 
        (args.from === undefined && args.to !== undefined)) {
      console.error('Both --from and --to must be specified together.');
      return;
    }
    
    // Initialize database connection
    try {
      await AppDataSource.initialize();
      console.log('Database connection established');
    } catch (dbError) {
      console.error('Failed to initialize database connection:', dbError);
      return;
    }
    
    // Interactive mode
    if (args.list) {
      await showInteractiveMenu();
      return;
    }
    
    // Project mode
    if (args.project) {
      const conversationIds = await getConversationIdsByProject(args.project);
      
      if (conversationIds.length === 0) {
        console.log(`No conversations found for project ${args.project}`);
        return;
      }
      
      console.log(`Found ${conversationIds.length} conversations for project ${args.project}`);
      await reprocessMultipleConversations(conversationIds);
      return;
    }
    
    // Team mode
    if (args.team) {
      const conversationIds = await getConversationIdsByTeam(args.team);
      
      if (conversationIds.length === 0) {
        console.log(`No conversations found for team ${args.team}`);
        return;
      }
      
      console.log(`Found ${conversationIds.length} conversations for team ${args.team}`);
      await reprocessMultipleConversations(conversationIds);
      return;
    }
    
    // Single conversation mode
    if (args.id) {
      await reprocessConversationInsights(args.id);
      return;
    }
    
    // Range mode
    if (args.from !== undefined && args.to !== undefined) {
      if (args.from > args.to) {
        console.error('Invalid range: "from" must be less than or equal to "to"');
        return;
      }
      
      const conversationIds: number[] = [];
      
      for (let id = args.from; id <= args.to; id++) {
        conversationIds.push(id);
      }
      
      await reprocessMultipleConversations(conversationIds);
      return;
    }
    
    // No valid arguments provided
    console.log('No valid arguments provided. Use --help for usage information.');
    showHelp();
  } catch (error) {
    console.error('Script execution failed:', error);
  } finally {
    // Ensure clean exit
    if (AppDataSource && AppDataSource.isInitialized) {
      try {
        await AppDataSource.destroy();
        console.log('Database connection closed');
      } catch (dbError) {
        console.error('Error closing database connection:', dbError);
      }
    }
    
    // Close the queue gracefully
    if (insightQueue) {
      try {
        await insightQueue.close();
        console.log('Insight queue closed');
      } catch (queueError) {
        console.error('Error closing queue:', queueError);
      }
    }
  }
}

// Run the script with proper error handling
main().catch(error => {
  console.error('Unhandled error in main function:', error);
  process.exit(1);
});

// Handle unhandled promise rejections globally
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled promise rejection:', reason);
});
