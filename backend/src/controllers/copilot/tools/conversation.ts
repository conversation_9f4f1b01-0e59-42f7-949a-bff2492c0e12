import { z } from 'zod';
import { AppDataSource } from '../../../data-source';
import { Conversation } from '../../../models/conversation';
import { AuthRequest } from '../../../middleware/auth.middleware';
import { RoleService } from '../../../services/role.service';

/**
 * Tool to fetch a conversation by ID
 * 
 * @param req Express request object with auth data
 * @returns Tool definition
 */
export const getConversationTool = (req: AuthRequest) => ({
    description: 'Fetch a conversation by its ID from the database',
    parameters: z.object({ 
        conversationId: z.number().or(z.string()).describe('The ID of the conversation to fetch')
    }),
    execute: async ({ conversationId }: { conversationId: number | string }) => {
        try {
            // Convert string ID to number if needed
            const id = typeof conversationId === 'string' ? parseInt(conversationId, 10) : conversationId;
            
            // Check if the user has permission to access conversations
            const roleService = new RoleService();
            const hasAllPermission = await roleService.hasPermission(req.user!.roleId!, 'conversations', 'read', 'all');
            const hasTeamPermission = await roleService.hasPermission(req.user!.roleId!, 'conversations', 'read', 'team');
            const hasOwnPermission = await roleService.hasPermission(req.user!.roleId!, 'conversations', 'read', 'own');
            
            // Build the query based on permissions
            const queryBuilder = AppDataSource
                .getRepository(Conversation)
                .createQueryBuilder('conversation')
                .leftJoinAndSelect('conversation.team', 'team')
                .leftJoinAndSelect('conversation.contact', 'contact')
                .leftJoinAndSelect('conversation.source', 'source')
                .leftJoinAndSelect('conversation.tags', 'tags')
                .leftJoinAndSelect('conversation.participants', 'participants')
                .leftJoinAndSelect('participants.user', 'user')
                .leftJoinAndSelect('conversation.insights', 'insights')
                .where('conversation.id = :id', { id })
                .andWhere('conversation.organizationId = :orgId', { orgId: req.organization!.id });
            
            // Apply permission filters
            if (!hasAllPermission) {
                if (hasTeamPermission) {
                    // Team-level access: can see team's conversations
                    queryBuilder.andWhere('(conversation.teamId = :teamId)', { teamId: req.user!.teamId });
                } else if (hasOwnPermission) {
                    // Own-level access: can only see conversations where they are a participant
                    queryBuilder.andWhere('EXISTS (SELECT 1 FROM conversation_participant cp WHERE cp.conversationId = conversation.id AND cp.userId = :userId)', 
                        { userId: req.user!.id });
                } else {
                    // No permissions
                    return { error: 'You do not have permission to access this conversation' };
                }
            }
            
            // Execute the query
            const conversation = await queryBuilder.getOne();
            
            if (!conversation) {
                return { error: `ID ${conversationId} not found or you don't have permission to access it` };
            }
            
            // Format the response to include only necessary data
            return {
                id: conversation.id,
                externalId: conversation.externalId,
                medium: conversation.medium,
                created_at: conversation.created_at,
                raw: conversation.raw,
                updated_at: conversation.updated_at,
                processingStatus: conversation.processingStatus,
                sourceId: conversation.sourceId,
                
                // Include extracted data if available
                extracted_data: conversation.extracted_data,
                
                // Include team info if available
                team: conversation.team ? {
                    id: conversation.team.id,
                    name: conversation.team.name,
                    color: conversation.team.color
                } : null,
                
                // Include contact info if available
                contact: conversation.contact ? {
                    id: conversation.contact.id,
                    name: conversation.contact.name,
                    email: conversation.contact.email,
                    phone: conversation.contact.phone
                } : null,
                
                // Include tags
                tags: conversation.tags ? conversation.tags.map(tag => ({
                    id: tag.id,
                    name: tag.name,
                    color: tag.color
                })) : [],
                
                // Include participants
                participants: conversation.participants ? conversation.participants.map(participant => ({
                    id: participant.id,
                    participantType: participant.participantType,
                    user: participant.user ? {
                        id: participant.user.id,
                        name: participant.user.name,
                        email: participant.user.email
                    } : null
                })) : [],
                
                // Include insights count
                insights_count: conversation.insights ? conversation.insights.length : 0,
                
                // Include a truncated version of messages if available
                messages_preview: conversation.messages ? 
                    conversation.messages.slice(0, 3).map(msg => ({
                        role: msg.role,
                        content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '')
                    })) : []
            };
        } catch (error) {
            console.error('Error fetching conversation:', error);
            return { error: 'Failed to fetch conversation information' };
        }
    },
});
