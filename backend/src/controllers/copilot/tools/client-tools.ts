import { z } from 'zod';

/**
 * Client-side tool that starts user interaction
 */
export const askForConfirmationTool = {
    description: 'Ask the user for confirmation.',
    parameters: z.object({
        message: z.string().describe('The message to ask for confirmation.'),
    }),
};

/**
 * Client-side tool that is automatically executed on the client
 */
export const getLocationTool = {
    description:
        'Get the user location. Always ask for confirmation before using this tool.',
    parameters: z.object({}),
};
