import { AuthRequest } from '../../../middleware/auth.middleware';
import { getWeatherInformation } from './weather';
import { getContactTool } from './contact';
import { getProjectTool } from './project';
import { getConversationTool } from './conversation';
import { getInsightsTool } from './insights';
import { askForConfirmationTool, getLocationTool } from './client-tools';

/**
 * Get all tools for the copilot
 * 
 * @param req Express request object with auth data
 * @returns Object containing all available tools
 */
export const getCopilotTools = (req: AuthRequest): Record<string, any> => {
    return {
        // Server-side tools with execute functions
        getWeatherInformation,
        getContact: getContactTool(req),
        getProject: getProjectTool(req),
        getConversation: getConversationTool(req),
        getInsights: getInsightsTool(req),
        
        // Client-side tools
        askForConfirmation: askForConfirmationTool,
        getLocation: getLocationTool,
    };
};
