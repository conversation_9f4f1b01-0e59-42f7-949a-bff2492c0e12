import { z } from 'zod';
import { AppDataSource } from '../../../data-source';
import { Project } from '../../../models/project';
import { AuthRequest } from '../../../middleware/auth.middleware';
import { RoleService } from '../../../services/role.service';

/**
 * Tool to fetch a project by ID
 * 
 * @param req Express request object with auth data
 * @returns Tool definition
 */
export const getProjectTool = (req: AuthRequest) => ({
    description: 'Fetch a project by its ID from the database',
    parameters: z.object({ 
        projectId: z.number().or(z.string()).describe('The ID of the project to fetch')
    }),
    execute: async ({ projectId }: { projectId: number | string }) => {
        try {
            // Convert string ID to number if needed
            const id = typeof projectId === 'string' ? parseInt(projectId, 10) : projectId;
            
            // Check if the user has permission to access projects
            const roleService = new RoleService();
            const hasAllPermission = await roleService.hasPermission(req.user!.roleId!, 'projects', 'read', 'all');
            const hasTeamPermission = await roleService.hasPermission(req.user!.roleId!, 'projects', 'read', 'team');
            
            // Build the query based on permissions
            const queryBuilder = AppDataSource
                .getRepository(Project)
                .createQueryBuilder('project')
                .leftJoinAndSelect('project.teams', 'teams')
                .where('project.id = :id', { id })
                .andWhere('project.organizationId = :orgId', { orgId: req.organization!.id });
            
            // Apply permission filters
            if (!hasAllPermission) {
                if (hasTeamPermission) {
                    // Team-level access: can see projects associated with their team
                    queryBuilder.andWhere('EXISTS (SELECT 1 FROM project_teams_team ptt WHERE ptt.projectId = project.id AND ptt.teamId = :teamId)', 
                        { teamId: req.user!.teamId });
                } else {
                    // No permissions
                    return { error: 'You do not have permission to access this project' };
                }
            }
            
            // Execute the query
            const project = await queryBuilder.getOne();
            
            if (!project) {
                return { error: `Project with ID ${projectId} not found or you don't have permission to access it` };
            }
            
            return {
                id: project.id,
                name: project.name,
                language: project.language,
                languageName: project.languageName(),
                projectKnowledge: project.projectKnowledge,
                teams: project.teams.map(team => ({
                    id: team.id,
                    name: team.name,
                    color: team.color
                })),
                createdAt: project.createdAt
            };
        } catch (error) {
            console.error('Error fetching project:', error);
            return { error: 'Failed to fetch project information' };
        }
    },
});
