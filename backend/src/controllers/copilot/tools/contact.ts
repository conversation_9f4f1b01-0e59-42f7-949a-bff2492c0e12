import { z } from 'zod';
import { AppDataSource } from '../../../data-source';
import { Contact } from '../../../models/contact';
import { Request } from 'express';
import { AuthRequest } from '../../../middleware/auth.middleware';
import { RoleService } from '../../../services/role.service';

/**
 * Tool to fetch a contact by ID
 * 
 * @param req Express request object with auth data
 * @returns Tool definition
 */
export const getContactTool = (req: AuthRequest) => ({
    description: 'Fetch a contact by their ID from the database',
    parameters: z.object({ 
        contactId: z.number().or(z.string()).describe('The ID of the contact to fetch')
    }),
    execute: async ({ contactId }: { contactId: number | string }) => {
        try {
            // Convert string ID to number if needed
            const id = typeof contactId === 'string' ? parseInt(contactId, 10) : contactId;
            
            // Check if the user has permission to access contacts
            const roleService = new RoleService();
            const hasAllPermission = await roleService.hasPermission(req.user!.roleId!, 'contacts', 'read', 'all');
            const hasTeamPermission = await roleService.hasPermission(req.user!.roleId!, 'contacts', 'read', 'team');
            const hasOwnPermission = await roleService.hasPermission(req.user!.roleId!, 'contacts', 'read', 'own');
            
            // Build the query based on permissions
            const queryBuilder = AppDataSource
                .getRepository(Contact)
                .createQueryBuilder('contact')
                .where('contact.id = :id', { id })
                .andWhere('contact.organizationId = :orgId', { orgId: req.organization!.id });
            
            // Apply permission filters
            if (!hasAllPermission) {
                if (hasTeamPermission) {
                    // Team-level access: can see contacts associated with their team
                    queryBuilder.andWhere('contact.teamId = :teamId', { teamId: req.user!.teamId });
                } else if (hasOwnPermission) {
                    // Own-level access: can only see contacts they created or are assigned to
                    queryBuilder.andWhere('(contact.createdById = :userId OR contact.assignedToId = :userId)', 
                        { userId: req.user!.id });
                } else {
                    // No permissions
                    return { error: 'You do not have permission to access this contact' };
                }
            }
            
            // Execute the query
            const contact = await queryBuilder.getOne();
            
            if (!contact) {
                return { error: `Contact with ID ${contactId} not found or you don't have permission to access it` };
            }
            
            return {
                id: contact.id,
                name: contact.name,
                email: contact.email,
                phone: contact.phone,
                createdAt: contact.createdAt
            };
        } catch (error) {
            console.error('Error fetching contact:', error);
            return { error: 'Failed to fetch contact information' };
        }
    },
});
