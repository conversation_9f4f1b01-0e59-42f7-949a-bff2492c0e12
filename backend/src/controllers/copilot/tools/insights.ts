import { z } from 'zod';
import { AppDataSource } from '../../../data-source';
import { Insight } from '../../../models/insight';
import { AuthRequest } from '../../../middleware/auth.middleware';
import { Conversation } from '../../../models/conversation';
import { RoleService } from '../../../services/role.service';

/**
 * Tool to fetch insights by conversation ID
 * 
 * @param req Express request object with auth data
 * @returns Tool definition
 */
export const getInsightsTool = (req: AuthRequest) => ({
    description: 'Fetch insights related to a specific conversation by its ID from the database',
    parameters: z.object({ 
        conversationId: z.number().or(z.string()).describe('The ID of the conversation to fetch insights for')
    }),
    execute: async ({ conversationId }: { conversationId: number | string }) => {
        try {
            // Convert string ID to number if needed
            const id = typeof conversationId === 'string' ? parseInt(conversationId, 10) : conversationId;
            
            // Check if the user has permission to access insights
            const roleService = new RoleService();
            const hasAllPermission = await roleService.hasPermission(req.user!.roleId!, 'insights', 'read', 'all');
            const hasTeamPermission = await roleService.hasPermission(req.user!.roleId!, 'insights', 'read', 'team');
            const hasOwnPermission = await roleService.hasPermission(req.user!.roleId!, 'insights', 'read', 'own');
            
            // First check if the conversation exists and if the user has permission to access it
            const conversationQueryBuilder = AppDataSource
                .getRepository(Conversation)
                .createQueryBuilder('conversation')
                .where('conversation.id = :id', { id })
                .andWhere('conversation.organizationId = :orgId', { orgId: req.organization!.id });
            
            // Apply permission filters for conversation access
            if (!hasAllPermission) {
                if (hasTeamPermission) {
                    // Team-level access: can see team's conversations
                    conversationQueryBuilder.andWhere('(conversation.teamId = :teamId)', { teamId: req.user!.teamId });
                } else if (hasOwnPermission) {
                    // Own-level access: can only see conversations where they are a participant
                    conversationQueryBuilder.andWhere('EXISTS (SELECT 1 FROM conversation_participant cp WHERE cp.conversationId = conversation.id AND cp.userId = :userId)', 
                        { userId: req.user!.id });
                } else {
                    // No permissions
                    return { error: 'You do not have permission to access insights for this conversation' };
                }
            }
            
            const conversation = await conversationQueryBuilder.getOne();
                
            if (!conversation) {
                return { error: `Conversation with ID ${conversationId} not found or you don't have permission to access it` };
            }
            
            // Build the query for insights
            const insightsQueryBuilder = AppDataSource
                .getRepository(Insight)
                .createQueryBuilder('insight')
                .leftJoinAndSelect('insight.definition', 'definition')
                .where('insight.conversationId = :id', { id })
                .orderBy('insight.created_at', 'DESC');
            
            // Execute the query
            const insights = await insightsQueryBuilder.getMany();
            
            if (!insights || insights.length === 0) {
                return { error: `No insights found for conversation with ID ${conversationId}` };
            }
            
            // Format the response to include only necessary data
            return {
                conversationId: id,
                count: insights.length,
                insights: insights.map(insight => ({
                    id: insight.id,
                    definitionId: insight.definitionId,
                    definitionName: insight.definition?.name || 'Unknown',
                    definitionType: insight.definition?.type || 'Unknown',
                    result: insight.result,
                    createdAt: insight.created_at
                }))
            };
        } catch (error) {
            console.error('Error fetching insights:', error);
            return { error: 'Failed to fetch insights information' };
        }
    },
});
