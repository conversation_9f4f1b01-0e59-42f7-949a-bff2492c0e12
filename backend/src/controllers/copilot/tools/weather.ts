import { z } from 'zod';

/**
 * Tool to get weather information for a city
 */
export const getWeatherInformation = {
    description: 'show the weather in a given city to the user',
    parameters: z.object({ city: z.string() }),
    execute: async ({ city }: { city: string }) => {
        const weatherOptions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy'];
        const weather = weatherOptions[
            Math.floor(Math.random() * weatherOptions.length)
        ];
        return `The weather in ${city} is ${weather}`;
    },
};
