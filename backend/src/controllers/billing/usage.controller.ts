import { Request, Response } from 'express';
import { UsageService } from '../../services/billing/usage.service';
import { hasPermission } from '../../decorators/permission.decorator';

/**
 * Controller for handling organization usage-related requests
 */
export class UsageController {
  private usageService: UsageService;

  constructor() {
    this.usageService = new UsageService();
  }

  /**
   * Get the current usage statistics for an organization
   * @param req Express request object
   * @param res Express response object
   */
  @hasPermission('organization:read:usage')
  async getCurrentUsage(req: Request, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      
      if (isNaN(organizationId)) {
        res.status(400).json({ error: 'Invalid organization ID' });
        return;
      }

      const currentUsage = await this.usageService.getCurrentUsage(organizationId);
      res.status(200).json(currentUsage);
    } catch (error) {
      console.error('Error getting current usage:', error);
      if (error.message?.includes('No active quota period found')) {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    }
  }

  /**
   * Check if an organization has reached usage limits
   * @param req Express request object
   * @param res Express response object
   */
  @hasPermission('organization:read:usage')
  async checkUsageLimits(req: Request, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const resourceType = req.query.resourceType as 'insights' | 'transcriptionHours';

      if (isNaN(organizationId)) {
        res.status(400).json({ error: 'Invalid organization ID' });
        return;
      }

      if (!resourceType || !['insights', 'transcriptionHours'].includes(resourceType)) {
        res.status(400).json({ error: 'Invalid resource type' });
        return;
      }

      const hasReachedLimit = await this.usageService.hasReachedLimit(organizationId, resourceType);
      res.status(200).json({ hasReachedLimit });
    } catch (error) {
      console.error('Error checking usage limits:', error);
      if (error.message?.includes('No active quota period found')) {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    }
  }
}
