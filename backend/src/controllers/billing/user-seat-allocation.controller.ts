import { Request, Response } from 'express';
import { AppDataSource } from '../../data-source';
import { UserSeatAllocation } from '../../models/billing/user-seat-allocation.entity';
import { User } from '../../models/user';
import { hasPermission } from '../../decorators/permission.decorator';
import { SeatManagementService } from '../../services/billing/seat-management.service';
import { UsageQuotaService } from '../../services/billing/usage-quota.service';
import { UsageQuota } from '../../models/billing/usage-quota.entity';

/**
 * Controller for user seat allocation operations
 */
export class UserSeatAllocationController {
  private seatManagementService: SeatManagementService;

  constructor() {
    const userSeatAllocationRepository = AppDataSource.getRepository(UserSeatAllocation);
    const usageQuotaRepository = AppDataSource.getRepository(UsageQuota);
    const usageQuotaService = new UsageQuotaService(usageQuotaRepository);
    
    this.seatManagementService = new SeatManagementService(
      userSeatAllocationRepository,
      usageQuotaService
    );
  }

  /**
   * Get all users with their seat allocation status
   * @param req Express request object
   * @param res Express response object
   */
  @hasPermission('billing:read:all')
  async getUsersWithSeatAllocations(req: Request, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      
      if (isNaN(organizationId)) {
        res.status(400).json({ error: 'Invalid organization ID' });
        return;
      }

      // Get all users in the organization
      const users = await AppDataSource
        .getRepository(User)
        .find({
          where: { organizationId },
          select: ['id', 'name', 'email', 'profileImagePath', 'profileImageType']
        });

      // Get active seat allocations
      const activeAllocations = await this.seatManagementService.getActiveAllocations(organizationId);
      
      // Map of user IDs to their seat allocation
      const userAllocations = new Map();
      activeAllocations.forEach(allocation => {
        userAllocations.set(allocation.userId, allocation);
      });

      // Combine user data with seat allocation info
      const usersWithSeatInfo = users.map(user => {
        const allocation = userAllocations.get(user.id);
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          profileImagePath: user.profileImagePath,
          profileImageType: user.profileImageType,
          hasSeat: !!allocation,
          allocationDate: allocation ? allocation.allocationDate : null
        };
      });

      res.status(200).json(usersWithSeatInfo);
    } catch (error) {
      console.error('Error getting users with seat allocations:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
