import { Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { StripeService } from '../../services/billing/stripe.service';
import { SubscriptionsService } from '../../services/billing/subscriptions.service';
import { InvoicesService } from '../../services/billing/invoices.service';
import { Subscription } from '../../models/billing/subscription.entity';
import { PlanTier } from '../../models/billing/plan-tier.entity';
import { UsageQuota } from '../../models/billing/usage-quota.entity';
import { Invoice } from '../../models/billing/invoice.entity';
import { UsageQuotaService } from '../../services/billing/usage-quota.service';
import { AppDataSource } from '../../data-source';

// Extended request interface for Stripe webhooks
interface StripeWebhookRequest extends AuthRequest {
  rawBody?: Buffer;
}

/**
 * Controller for handling Stripe webhooks
 * Processes Stripe events like subscription updates, payment successes, etc.
 */
export class WebhookController {
  private stripeService: StripeService;
  private subscriptionsService: SubscriptionsService;
  private invoicesService: InvoicesService;

  constructor() {
    // Initialize repositories
    const subscriptionRepository = AppDataSource.getRepository(Subscription);
    const planTierRepository = AppDataSource.getRepository(PlanTier);
    const usageQuotaRepository = AppDataSource.getRepository(UsageQuota);
    const invoiceRepository = AppDataSource.getRepository(Invoice);
    
    // Initialize services
    const configService = {
      get: (key: string) => {
        if (key === 'STRIPE_SECRET_KEY') {
          return process.env.STRIPE_SECRET_KEY;
        } else if (key === 'STRIPE_WEBHOOK_SECRET') {
          return process.env.STRIPE_WEBHOOK_SECRET;
        }
        return process.env[key];
      }
    };
    
    this.stripeService = new StripeService(configService);
    const usageQuotaService = new UsageQuotaService(usageQuotaRepository);
    
    this.subscriptionsService = new SubscriptionsService(
      subscriptionRepository,
      planTierRepository,
      this.stripeService,
      usageQuotaService
    );
    
    this.invoicesService = new InvoicesService(
      invoiceRepository,
      subscriptionRepository
    );
  }

  /**
   * Handle Stripe webhook events
   */
  public async handleWebhook(req: StripeWebhookRequest, res: Response): Promise<void> {
    try {
      const sig = req.headers['stripe-signature'] as string;
      
      if (!sig) {
        res.status(400).json({ error: 'Stripe signature is missing' });
        return;
      }
      
      const payload = req.rawBody ? req.rawBody.toString() : '';
      
      if (!payload) {
        res.status(400).json({ error: 'Request body is empty' });
        return;
      }
      
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      
      if (!webhookSecret) {
        res.status(500).json({ error: 'Webhook secret is not configured' });
        return;
      }
      
      // Construct the event from the payload and signature
      // Note: Stripe SDK will verify the signature
      const stripe = new (require('stripe'))(process.env.STRIPE_SECRET_KEY);
      const event = stripe.webhooks.constructEvent(payload, sig, webhookSecret);
      
      // Process the event
      await this.processWebhookEvent(event);
      
      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Webhook error:', error.message);
      res.status(400).json({ error: error.message });
    }
  }

  /**
   * Process a Stripe webhook event
   */
  private async processWebhookEvent(event: any): Promise<void> {
    const eventType = event.type;
    
    switch (eventType) {
      case 'customer.subscription.created':
        await this.handleSubscriptionCreated(event.data.object);
        break;
      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        await this.handleSubscriptionDeleted(event.data.object);
        break;
      case 'invoice.paid':
        await this.handleInvoicePaid(event.data.object);
        break;
      case 'invoice.payment_failed':
        await this.handleInvoicePaymentFailed(event.data.object);
        break;
      default:
        console.log(`Unhandled event type: ${eventType}`);
    }
  }

  /**
   * Handle subscription created webhook event
   */
  private async handleSubscriptionCreated(subscription: any): Promise<void> {
    try {
      // Find subscription in our database or create it
      const stripeId = subscription.id;
      const organizationId = parseInt(subscription.metadata?.organizationId);
      const planTierId = parseInt(subscription.metadata?.planTierId);
      const seats = subscription.metadata?.seats ? parseInt(subscription.metadata.seats) : 1;
      
      if (organizationId && planTierId) {
        await this.subscriptionsService.create(
          organizationId,
          planTierId,
          seats,
          subscription.customer
        );
      }
    } catch (error) {
      console.error('Error handling subscription created:', error);
    }
  }

  /**
   * Handle subscription updated webhook event
   */
  private async handleSubscriptionUpdated(subscription: any): Promise<void> {
    try {
      // Find all subscriptions for the organization and filter by stripeId
      const stripeId = subscription.id;
      const subscriptionRepo = AppDataSource.getRepository(Subscription);
      const existingSubscription = await subscriptionRepo.findOne({ 
        where: { stripeSubscriptionId: stripeId } 
      });
      
      if (existingSubscription) {
        // Update seats if metadata has them
        if (subscription.metadata?.seats) {
          const seats = parseInt(subscription.metadata.seats);
          await this.subscriptionsService.updateSeats(existingSubscription.id, seats);
        }
        
        // Update status based on Stripe subscription status
        if (subscription.status === 'active' || subscription.status === 'trialing') {
          // Update active status
        } else if (subscription.cancel_at_period_end) {
          await this.subscriptionsService.cancelAtPeriodEnd(existingSubscription.id);
        }
      }
    } catch (error) {
      console.error('Error handling subscription updated:', error);
    }
  }

  /**
   * Handle subscription deleted webhook event
   */
  private async handleSubscriptionDeleted(subscription: any): Promise<void> {
    try {
      // Find subscription by Stripe ID
      const stripeId = subscription.id;
      const subscriptionRepo = AppDataSource.getRepository(Subscription);
      const existingSubscription = await subscriptionRepo.findOne({ 
        where: { stripeSubscriptionId: stripeId } 
      });
      
      if (existingSubscription) {
        await this.subscriptionsService.cancelAtPeriodEnd(existingSubscription.id);
      }
    } catch (error) {
      console.error('Error handling subscription deleted:', error);
    }
  }

  /**
   * Handle invoice paid webhook event
   */
  private async handleInvoicePaid(invoice: any): Promise<void> {
    try {
      // Create or update invoice in our database
      const stripeInvoiceId = invoice.id;
      const stripeSubscriptionId = invoice.subscription;
      
      if (stripeSubscriptionId) {
        // Find subscription by Stripe ID
        const subscriptionRepo = AppDataSource.getRepository(Subscription);
        const subscription = await subscriptionRepo.findOne({ 
          where: { stripeSubscriptionId: stripeSubscriptionId } 
        });
        
        if (subscription) {
          // Create new invoice record with properly typed status
          const newInvoice = {
            organizationId: subscription.organizationId,
            subscriptionId: subscription.id,
            stripeInvoiceId: stripeInvoiceId,
            amount: invoice.amount_paid / 100, // Convert from cents to dollars
            currency: invoice.currency,
            status: 'paid' as 'paid', // Explicitly cast to the enum type
            dueDate: new Date(invoice.due_date * 1000) // Convert from Unix timestamp
          };
          
          // Add URLs to the invoice if available
          if (invoice.hosted_invoice_url) {
            (newInvoice as any).invoiceUrl = invoice.hosted_invoice_url;
          }
          
          if (invoice.invoice_pdf) {
            (newInvoice as any).invoicePdf = invoice.invoice_pdf;
          }
          
          // Use repository directly to create the invoice
          const invoiceRepo = AppDataSource.getRepository(Invoice);
          await invoiceRepo.save(newInvoice);
        }
      }
    } catch (error) {
      console.error('Error handling invoice paid:', error);
    }
  }

  /**
   * Handle invoice payment failed webhook event
   */
  private async handleInvoicePaymentFailed(invoice: any): Promise<void> {
    try {
      // Find invoice by Stripe ID and update its status
      const stripeInvoiceId = invoice.id;
      const invoiceRepo = AppDataSource.getRepository(Invoice);
      
      const existingInvoice = await invoiceRepo.findOne({ where: { stripeInvoiceId } });
      
      if (existingInvoice) {
        // Use a valid status from the Invoice entity enum
        existingInvoice.status = 'uncollectible' as 'uncollectible';
        await invoiceRepo.save(existingInvoice);
      }
    } catch (error) {
      console.error('Error handling invoice payment failed:', error);
    }
  }
}
