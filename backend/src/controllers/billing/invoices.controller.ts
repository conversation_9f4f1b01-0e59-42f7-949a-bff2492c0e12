import { Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { InvoicesService } from '../../services/billing/invoices.service';
import { hasPermission } from '../../decorators/permission.decorator';
import { Invoice } from '../../models/billing/invoice.entity';
import { Subscription } from '../../models/billing/subscription.entity';
import { AppDataSource } from '../../data-source';
import { StripeService } from '../../services/billing/stripe.service';

/**
 * Controller for invoice management
 * Provides endpoints for retrieving invoices and invoice metrics
 */
export class InvoicesController {
  private invoicesService: InvoicesService;
  private stripeService: StripeService;

  constructor() {
    // Initialize repositories
    const invoiceRepository = AppDataSource.getRepository(Invoice);
    const subscriptionRepository = AppDataSource.getRepository(Subscription);
    
    // Initialize services
    const configService = {
      get: (key: string) => {
        if (key === 'STRIPE_SECRET_KEY') {
          return process.env.STRIPE_SECRET_KEY;
        }
        return process.env[key];
      }
    };
    
    this.stripeService = new StripeService(configService);
    this.invoicesService = new InvoicesService(
      invoiceRepository,
      subscriptionRepository
    );
  }

  /**
   * Get all invoices for an organization
   */
  @hasPermission('billing:read:all')
  public async findAllByOrganization(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const invoices = await this.invoicesService.findAllByOrganization(organizationId);
      res.json(invoices);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Get invoice by ID
   */
  @hasPermission('billing:read:all')
  public async findOne(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const invoice = await this.invoicesService.findById(id);
      res.json(invoice);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }
  
  /**
   * Get invoice metrics for an organization
   */
  @hasPermission('billing:read:all')
  public async getInvoiceMetrics(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const metrics = await this.invoicesService.getInvoiceMetrics(organizationId);
      res.json(metrics);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Download invoice PDF
   */
  @hasPermission('billing:read:all')
  public async downloadInvoice(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const invoice = await this.invoicesService.findById(id);
      
      if (!invoice.stripeInvoiceId) {
        throw new Error('Invoice does not have a Stripe invoice ID');
      }
      
      // Get invoice PDF from Stripe
      const pdfBuffer = await this.stripeService.getInvoicePdf(invoice.stripeInvoiceId);
      
      // Set response headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="invoice-${invoice.stripeInvoiceId}.pdf"`);
      res.setHeader('Content-Length', pdfBuffer.length);
      
      // Send the PDF
      res.send(pdfBuffer);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }
}
