import { Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { PlansService } from '../../services/billing/plans.service';
import { hasPermission } from '../../decorators/permission.decorator';
import { Plan } from '../../models/billing/plan.entity';
import { PlanTier } from '../../models/billing/plan-tier.entity';
import { AppDataSource } from '../../data-source';

/**
 * Controller for plans management
 * Provides endpoints for retrieving available subscription plans
 */
export class PlansController {
  private plansService: PlansService;

  constructor() {
    const planRepository = AppDataSource.getRepository(Plan);
    const planTierRepository = AppDataSource.getRepository(PlanTier);
    
    this.plansService = new PlansService(planRepository, planTierRepository);
  }

  /**
   * Get all active plans
   */
  @hasPermission('billing:read:all')
  public async findAll(req: AuthRequest, res: Response): Promise<void> {
    try {
      const includeInactive = req.query.includeInactive === 'true';
      const plans: Plan[] = await this.plansService.findAll(includeInactive);
      res.json(plans);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Get plan by ID
   */
  @hasPermission('billing:read:all')
  public async findOne(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const plan: Plan = await this.plansService.findById(id);
      res.json(plan);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Get all plan tiers for a plan
   */
  @hasPermission('billing:read:all')
  public async findTiers(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const tiers: PlanTier[] = await this.plansService.findTiersByPlanId(id);
      res.json(tiers);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Get available plans for an organization
   */
  @hasPermission('billing:read:all')
  public async findForOrganization(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const plans: Plan[] = await this.plansService.findForOrganization(organizationId);
      res.json(plans);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
}
