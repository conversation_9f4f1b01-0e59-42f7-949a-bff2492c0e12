import { Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { SubscriptionsService } from '../../services/billing/subscriptions.service';
import { hasPermission } from '../../decorators/permission.decorator';
import { Subscription } from '../../models/billing/subscription.entity';
import { PlanTier } from '../../models/billing/plan-tier.entity';
import { StripeService } from '../../services/billing/stripe.service';
import { UsageQuotaService } from '../../services/billing/usage-quota.service';
import { UsageQuota } from '../../models/billing/usage-quota.entity';
import { AppDataSource } from '../../data-source';

/**
 * Controller for subscription management
 * Provides endpoints for creating, updating, and canceling subscriptions
 */
export class SubscriptionsController {
  private subscriptionsService: SubscriptionsService;

  constructor() {
    const subscriptionRepository = AppDataSource.getRepository(Subscription);
    const planTierRepository = AppDataSource.getRepository(PlanTier);
    const usageQuotaRepository = AppDataSource.getRepository(UsageQuota);
    
    // Initialize services
    const configService = {
      get: (key: string) => {
        if (key === 'STRIPE_SECRET_KEY') {
          return process.env.STRIPE_SECRET_KEY;
        }
        return process.env[key];
      }
    };
    
    const stripeService = new StripeService(configService);
    const usageQuotaService = new UsageQuotaService(usageQuotaRepository);
    
    this.subscriptionsService = new SubscriptionsService(
      subscriptionRepository,
      planTierRepository,
      stripeService,
      usageQuotaService
    );
  }

  /**
   * Get all subscriptions for an organization
   */
  @hasPermission('billing:read:all')
  public async findAllByOrganization(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const subscriptions = await this.subscriptionsService.findAllByOrganization(organizationId);
      res.json(subscriptions);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Get active subscription for an organization
   */
  @hasPermission('billing:read:all')
  public async findActiveByOrganization(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const subscription = await this.subscriptionsService.findActiveByOrganization(organizationId);
      res.json(subscription);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Get subscription by ID
   */
  @hasPermission('billing:read:all')
  public async findOne(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const subscription = await this.subscriptionsService.findById(id);
      res.json(subscription);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Create a new subscription
   */
  @hasPermission('billing:create:all')
  public async create(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { organizationId, planTierId, seats, stripeCustomerId } = req.body;
      
      const subscription = await this.subscriptionsService.create(
        organizationId,
        planTierId,
        seats,
        stripeCustomerId
      );
      
      res.status(201).json(subscription);
    } catch (error) {
      if (error.name === 'BadRequestError') {
        res.status(400).json({ message: error.message });
      } else if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Update seats for a subscription
   */
  @hasPermission('billing:update:all')
  public async updateSeats(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const { seats } = req.body;
      
      const subscription = await this.subscriptionsService.updateSeats(id, seats);
      
      res.json(subscription);
    } catch (error) {
      if (error.name === 'BadRequestError') {
        res.status(400).json({ message: error.message });
      } else if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Change plan tier for a subscription
   */
  @hasPermission('billing:update:all')
  public async changePlanTier(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const { planTierId } = req.body;
      
      const subscription = await this.subscriptionsService.changePlanTier(id, planTierId);
      
      res.json(subscription);
    } catch (error) {
      if (error.name === 'BadRequestError') {
        res.status(400).json({ message: error.message });
      } else if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Cancel a subscription at period end
   */
  @hasPermission('billing:delete:all')
  public async cancelAtPeriodEnd(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      const subscription = await this.subscriptionsService.cancelAtPeriodEnd(id);
      
      res.json(subscription);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Reactivate a subscription that was set to cancel
   */
  @hasPermission('billing:update:all')
  public async reactivate(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      const subscription = await this.subscriptionsService.reactivate(id);
      
      res.json(subscription);
    } catch (error) {
      if (error.name === 'BadRequestError') {
        res.status(400).json({ message: error.message });
      } else if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Get subscription metrics
   */
  @hasPermission('billing:read:all')
  public async getMetrics(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      
      const metrics = await this.subscriptionsService.getSubscriptionMetrics(organizationId);
      
      res.json(metrics);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }
}
