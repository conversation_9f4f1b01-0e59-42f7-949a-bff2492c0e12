import { Request, Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { hasPermission } from '../../decorators/permission.decorator';
import { PaymentMethodsService } from '../../services/billing/payment-methods.service';
import { StripeService } from '../../services/billing/stripe.service';
import { PaymentMethod } from '../../models/billing/payment-method.entity';
import { AppDataSource } from '../../data-source';

/**
 * Controller for payment methods management
 * Provides endpoints for adding, updating, and removing payment methods
 */
export class PaymentMethodsController {
  private paymentMethodsService: PaymentMethodsService;
  private stripeService: StripeService;

  constructor() {
    const paymentMethodRepository = AppDataSource.getRepository(PaymentMethod);
    
    // Initialize services
    const configService = {
      get: (key: string) => {
        if (key === 'STRIPE_SECRET_KEY') {
          return process.env.STRIPE_SECRET_KEY;
        }
        return process.env[key];
      }
    };
    
    this.stripeService = new StripeService(configService);
    this.paymentMethodsService = new PaymentMethodsService(paymentMethodRepository, this.stripeService);
  }

  /**
   * Get all payment methods for an organization
   */
  @hasPermission('billing:read:all')
  public async findAllByOrganization(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const paymentMethods = await this.paymentMethodsService.findAllByOrganization(organizationId);
      res.json(paymentMethods);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Create a setup intent for adding a payment method
   */
  @hasPermission('billing:read:all')
  public async createSetupIntent(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { organizationId, stripeCustomerId } = req.body;
      const setupIntent = await this.paymentMethodsService.createSetupIntent(
        organizationId,
        stripeCustomerId
      );
      res.status(201).json(setupIntent);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Add a payment method
   */
  @hasPermission('billing:create:all')
  public async addPaymentMethod(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { organizationId, stripePaymentMethodId, stripeCustomerId, setAsDefault } = req.body;
      const paymentMethod = await this.paymentMethodsService.addPaymentMethod(
        organizationId,
        stripePaymentMethodId,
        stripeCustomerId,
        setAsDefault
      );
      res.status(201).json(paymentMethod);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  /**
   * Set a payment method as default
   */
  @hasPermission('billing:update:all')
  public async setAsDefault(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const { stripeCustomerId } = req.body;
      const paymentMethod = await this.paymentMethodsService.setAsDefault(id, stripeCustomerId);
      res.json(paymentMethod);
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Delete a payment method
   */
  @hasPermission('billing:delete:all')
  public async delete(req: AuthRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const stripeCustomerId = req.query.stripeCustomerId as string;
      await this.paymentMethodsService.delete(id, stripeCustomerId);
      res.status(204).send();
    } catch (error) {
      if (error.name === 'NotFoundError') {
        res.status(404).json({ message: error.message });
      } else if (error.name === 'BadRequestError') {
        res.status(400).json({ message: error.message });
      } else {
        res.status(500).json({ message: error.message });
      }
    }
  }

  /**
   * Sync payment methods from Stripe
   */
  @hasPermission('billing:update:all')
  public async syncFromStripe(req: AuthRequest, res: Response): Promise<void> {
    try {
      const organizationId = parseInt(req.params.organizationId);
      const { stripeCustomerId } = req.body;
      const paymentMethods = await this.paymentMethodsService.syncFromStripe(
        organizationId,
        stripeCustomerId
      );
      res.json(paymentMethods);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
}
