import { Response, NextFunction } from 'express';
import { Contact } from '../../models/contact';
import { AuthRequest } from '../../middleware/auth.middleware';
import { AppDataSource } from '../../data-source';
import { ContactEventService } from '../../services/contact-event';
import { hasPermission } from '../../decorators/permission.decorator';
import { audited } from '../../decorators/audit.decorator';
import { throwAPIError } from '../../middleware/error-handler.middleware';
import { BaseController } from '../base.controller';

interface CreateContactBody {
    name: string;
    email?: string;
    phone?: string;
    accountId?: number | null;
    customFields?: Record<string, any>;
}

interface UpdateContactBody extends Partial<CreateContactBody> {}

/**
 * Contact Controller class handling all contact-related operations
 */
export class ContactController extends BaseController {
    private contacts = AppDataSource.getRepository(Contact);

    constructor() {
        super(); // Call BaseController constructor for auto-wrapping
    }
    /**
     * List all contacts for a organization
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:read:own')
    @audited({
        action: 'read',
        resource: 'contact',
        includeRequest: false,
        includeResponse: false
    })
    public async findAll(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const page = parseInt(req.query.page as string) || 1;
            const perPage = parseInt(req.query.perPage as string) || 10;
            const unpaginated = req.query.unpaginated === 'true';


            const queryBuilder = this.contacts.createQueryBuilder('contact')
                .where('contact.organizationId = :orgId', { orgId: this.organizationId })
                .orderBy('contact.createdAt', 'DESC');

            // Get total count
            const totalItems = await queryBuilder.getCount();

            // If unpaginated, return all items
            if (unpaginated) {
                const contacts = await queryBuilder.getMany();
                res.json({
                    data: contacts,
                    meta: {
                        currentPage: 1,
                        totalPages: 1,
                        totalItems,
                        perPage: totalItems
                    }
                });
                return;
            }

            // Apply pagination
            const skip = (page - 1) * perPage;
            const contacts = await queryBuilder
                .skip(skip)
                .take(perPage)
                .getMany();

            const totalPages = Math.ceil(totalItems / perPage);

            res.json({
                data: contacts,
                meta: {
                    currentPage: page,
                    totalPages,
                    totalItems,
                    perPage
                }
            });
        } catch (error) {
            next(error);
        }
    }

    /**
     * Create a new contact
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:create:own')
    @audited({
        action: 'create',
        resource: 'contact',
        includeRequest: true,
        includeResponse: true,
        sensitiveFields: ['password']
    })
    public async create(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const { name, email, phone, accountId, customFields } = req.body as CreateContactBody;

            if (!name) {
                throwAPIError('Name is required', 400, 'VALIDATION_ERROR');
            }


            const contact = this.contacts.create({
                name,
                email,
                phone,
                accountId,
                organization: { id: this.organizationId },
                customFields: customFields || {}
            });

            const savedContact = await this.contacts.save(contact);
            
            // Create a contact creation event
            try {
                await ContactEventService.createContactCreationEvent(
                    savedContact,
                    req.user?.id
                );
            } catch (eventError) {
                console.error('Error creating contact event:', eventError);
                // Don't fail the contact creation if event creation fails
            }
            
            res.status(201).json(savedContact);
        } catch (error) {
            if (error.code === '23505') { // Unique constraint violation
                const field = error.detail?.includes('email') ? 'email' : 'phone';
                throwAPIError(
                    `A contact with this ${field} already exists in your organization`,
                    400,
                    'DUPLICATE_CONTACT'
                );
            }
            next(error);
        }
    }

    /**
     * Get a single contact by ID
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:read:own')
    @audited({
        action: 'read',
        resource: 'contact',
        entityIdPath: 'params.id',
        includeResponse: true
    })
    public async findOne(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {

            const contactId = parseInt(req.params.id);
            if (isNaN(contactId)) {
                throwAPIError('Invalid contact ID', 400, 'INVALID_ID');
            }

            const contact = await this.contacts.findOne({
                where: {
                    id: contactId,
                    organizationId: this.organizationId
                }
            });

            if (!contact) {
                throwAPIError('Contact not found', 404, 'CONTACT_NOT_FOUND');
            }

            res.json(contact);
        } catch (error) {
            next(error);
        }
    }

    /**
     * Update an existing contact
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:update:own')
    @audited({
        action: 'update',
        resource: 'contact',
        entityIdPath: 'params.id',
        includeRequest: true,
        includeResponse: true
    })
    public async update(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {

            const contactId = parseInt(req.params.id);
            if (isNaN(contactId)) {
                throwAPIError('Invalid contact ID', 400, 'INVALID_ID');
            }

            const { name, email, phone, accountId, customFields } = req.body as UpdateContactBody;
            if (!name) {
                throwAPIError('Name is required', 400, 'VALIDATION_ERROR');
            }

            const contact = await this.contacts.findOne({
                where: {
                    id: contactId,
                    organization: { id: this.organizationId }
                }
            });

            if (!contact) {
                throwAPIError('Contact not found', 404, 'CONTACT_NOT_FOUND');
            }

            // Track changes for event logging
            const changes: Record<string, {old: any, new: any}> = {};
            
            if (contact.name !== name) {
                changes.name = { old: contact.name, new: name };
            }
            
            if (contact.email !== email) {
                changes.email = { old: contact.email, new: email };
            }
            
            if (contact.phone !== phone) {
                changes.phone = { old: contact.phone, new: phone };
            }
            
            if (accountId !== undefined && contact.accountId !== accountId) {
                changes.accountId = { old: contact.accountId, new: accountId };
            }

            // Update contact fields
            contact.name = name;
            contact.email = email;
            contact.phone = phone;
            
            // Update accountId if provided
            if (accountId !== undefined) {
                contact.accountId = accountId;
            }
            
            // Update custom fields if provided
            if (customFields !== undefined) {
                contact.customFields = customFields;
            }

            const updatedContact = await this.contacts.save(contact);
            
            // Create a contact update event if there were changes
            if (Object.keys(changes).length > 0) {
                try {
                    await ContactEventService.createContactUpdateEvent(
                        updatedContact,
                        req.user?.id,
                        changes
                    );
                } catch (eventError) {
                    console.error('Error creating contact update event:', eventError);
                    // Don't fail the contact update if event creation fails
                }
            }
            
            res.json(updatedContact);
        } catch (error) {
            if (error.code === '23505') { // Unique constraint violation
                const field = error.detail?.includes('email') ? 'email' : 'phone';
                throwAPIError(
                    `A contact with this ${field} already exists in your organization`,
                    400,
                    'DUPLICATE_CONTACT'
                );
            }
            next(error);
        }
    }

    /**
     * Delete a contact
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:delete:own')
    @audited({
        action: 'delete',
        resource: 'contact',
        entityIdPath: 'params.id',
        capturePreState: true,
        capturePostState: false,
        auditOnError: true
    })
    public async destroy(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {

            const contactId = parseInt(req.params.id);
            if (isNaN(contactId)) {
                throwAPIError('Invalid contact ID', 400, 'INVALID_ID');
            }

            const contact = await this.contacts.findOne({
                where: {
                    id: contactId,
                    organization: { id: this.organizationId }
                }
            });

            if (!contact) {
                throwAPIError('Contact not found', 404, 'CONTACT_NOT_FOUND');
            }

            await this.contacts.remove(contact);
            res.status(204).send();
        } catch (error) {
            next(error);
        }
    }

    /**
     * Count all contacts for an organization
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:read:own')
    public async countContacts(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {

            const count = await this.contacts.count({
                where: {
                    organizationId: this.organizationId
                }
            });

            res.json({ count });
        } catch (error) {
            next(error);
        }
    }
}

// Create a singleton instance for export
export const contactController = new ContactController();
