import { Response, NextFunction } from 'express';
import { ContactEvent } from '../../models/contact-event';
import { Contact } from '../../models/contact';
import { AuthRequest } from '../../middleware/auth.middleware';
import { AppDataSource } from '../../data-source';
import { ContactEventService } from '../../services/contact-event';
import { BaseController } from '../base.controller';

/**
 * Contact Event Controller class handling all contact event-related operations
 */
export class ContactEventController extends BaseController {
  private contacts = AppDataSource.getRepository(Contact);
  private events = AppDataSource.getRepository(ContactEvent);

  constructor() {
    super(); // Call BaseController constructor for auto-wrapping
  }

  /**
   * List events for a contact with pagination
   * @param req - Express request object with authenticated user
   * @param res - Express response object
   * @param next - Express next function
   */
  public async listContactEvents(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const contactId = parseInt(req.params.contactId);
      
      // Parse pagination parameters
      const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
      const offset = req.query.offset ? parseInt(req.query.offset as string) : undefined;
      
      // Verify contact belongs to user's organization
      const contact = await this.contacts.findOne({
        where: {
          id: contactId,
          organizationId: this.organizationId
        }
      });

      if (!contact) {
        res.status(404).json({ error: 'Contact not found' });
        return;
      }

      const serviceResult =
        limit === undefined && offset === undefined
          ? await ContactEventService.listByContact(contactId)
          : await ContactEventService.listByContact(contactId, limit, offset);
      let events: ContactEvent[];
      let total: number | undefined;
      if (Array.isArray(serviceResult)) {
        events = serviceResult;
        total = undefined;
      } else {
        events = serviceResult.events;
        total = serviceResult.total;
      }

      // Check if we should render the events
      const renderEvents = req.query.render === 'true';

      // For backwards compatibility with older callers, return the plain array
      // when no pagination parameters or rendering is requested
      if (!renderEvents && limit === undefined && offset === undefined) {
        res.json(events);
        return;
      }

      if (renderEvents) {
        // Render each event
        const renderedEvents = events.map(event => ({
          ...event,
          rendered: ContactEventService.renderEvent(event)
        }));
        res.json({
          events: renderedEvents,
          total,
          limit,
          offset
        });
      } else {
        res.json({
          events,
          total,
          limit,
          offset
        });
      }
    } catch (error) {
      console.error('Error listing contact events:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new contact event
   * @param req - Express request object with authenticated user
   * @param res - Express response object
   * @param next - Express next function
   */
  public async createContactEvent(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const contactId = parseInt(req.params.contactId);
      
      // Verify contact belongs to user's organization
      const contact = await this.contacts.findOne({
        where: {
          id: contactId,
          organizationId: this.organizationId
        }
      });

      if (!contact) {
        res.status(404).json({ error: 'Contact not found' });
        return;
      }

      // Extract event data from request body
      const { 
        title, 
        description, 
        eventType, 
        provider, 
        conversationId, 
        sourceId, 
        eventDate, 
        metadata 
      } = req.body;

      // Create the event
      const event = await ContactEventService.createEvent({
        title,
        description,
        eventType,
        provider,
        contactId,
        conversationId,
        sourceId,
        createdById: req.user?.id,
        eventDate: eventDate ? new Date(eventDate) : undefined,
        metadata
      });

      res.status(201).json(event);
    } catch (error) {
      console.error('Error creating contact event:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new contact event using a provider
   * @param req - Express request object with authenticated user
   * @param res - Express response object
   * @param next - Express next function
   */
  public async createContactEventWithProvider(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const contactId = parseInt(req.params.contactId);
      const providerType = req.params.provider;
      
      // Verify contact belongs to user's organization
      const contact = await this.contacts.findOne({
        where: {
          id: contactId,
          organizationId: this.organizationId
        }
      });

      if (!contact) {
        res.status(404).json({ error: 'Contact not found' });
        return;
      }

      // Extract event data from request body and ensure contactId is set
      const eventData = {
        ...req.body,
        contactId,
        createdById: req.user?.id
      };

      // Create the event with the provider
      const event = await ContactEventService.createEventWithProvider(providerType, eventData);

      res.status(201).json(event);
    } catch (error) {
      console.error('Error creating contact event with provider:', error);
      
      // Handle specific errors
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
        return;
      }
      
      if (error instanceof Error && error.message.includes('Invalid event data')) {
        res.status(400).json({ error: error.message });
        return;
      }
      
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Render a contact event
   * @param req - Express request object with authenticated user
   * @param res - Express response object
   * @param next - Express next function
   */
  public async renderContactEvent(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const eventId = parseInt(req.params.id);
      
      // Get the event
      const event = await this.events.findOne({
        where: { id: eventId },
        relations: ['contact']
      });

      if (!event) {
        res.status(404).json({ error: 'Event not found' });
        return;
      }

      // Verify the contact belongs to the user's organization
      const contact = await this.contacts.findOne({
        where: {
          id: event.contactId,
          organizationId: this.organizationId
        }
      });

      if (!contact) {
        res.status(403).json({ error: 'Unauthorized' });
        return;
      }

      // Render the event
      const renderedEvent = ContactEventService.renderEvent(event);
      
      res.json(renderedEvent);
    } catch (error) {
      console.error('Error rendering contact event:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete a contact event
   * @param req - Express request object with authenticated user
   * @param res - Express response object
   * @param next - Express next function
   */
  public async deleteContactEvent(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const eventId = parseInt(req.params.id);
      
      // Get the event
      const event = await this.events.findOne({
        where: { id: eventId },
        relations: ['contact']
      });

      if (!event) {
        res.status(404).json({ error: 'Event not found' });
        return;
      }

      // Verify the contact belongs to the user's organization
      const contact = await this.contacts.findOne({
        where: {
          id: event.contactId,
          organizationId: this.organizationId
        }
      });

      if (!contact) {
        res.status(403).json({ error: 'Unauthorized' });
        return;
      }

      await ContactEventService.deleteEvent(eventId);
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting contact event:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}