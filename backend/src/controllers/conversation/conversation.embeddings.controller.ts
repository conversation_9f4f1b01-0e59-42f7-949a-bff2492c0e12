import { Response, NextFunction } from 'express';
import { ConversationEmbeddingsService } from '../../services/conversation-embeddings.service';
import { AuthRequest } from '../../middleware/auth.middleware';
import { BaseController } from '../base.controller';
import { hasPermission } from '../../decorators/permission.decorator';
import { throwAPIError } from '../../middleware/error-handler.middleware';



export class ConversationEmbeddingsController extends BaseController {
    private service: ConversationEmbeddingsService;

    constructor() {
        super(); // Call BaseController constructor for auto-wrapping
        this.service = new ConversationEmbeddingsService();
    }

    /**
     * Search conversation embeddings with semantic query
     * @param req Express request with authenticated user
     * @param res Express response
     * @param next Express next function
     */
    @hasPermission('conversations:read:own')
    public async searchEmbeddings(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const { query, limit = 100 } = req.body;
            
            if (!query) {
                throwAPIError('Query is required', 400, 'VALIDATION_ERROR');
            }

            // Get user ID from the request
            const userId = req.user?.id;
            
            if (!userId) {
                throwAPIError('User authentication is required', 401, 'UNAUTHORIZED');
            }

            const results = await this.service.searchEmbeddings(query, limit, this.organizationId, userId);
            res.json(results);
        } catch (error) {
            next(error);
        }
    }
}

// Create a singleton instance for export
export const conversationEmbeddingsController = new ConversationEmbeddingsController();