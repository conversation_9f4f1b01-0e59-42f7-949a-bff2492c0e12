import { Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { AppDataSource } from '../../data-source';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';
import QRCode from 'qrcode-svg';
import { renderInsight } from '../../lib/pdf-renderers';
import { InsightType } from '../../lib/pdf-renderers/types';
import { responseWrapper } from '../../utils/response-wrapper';

// Promisify exec for async/await usage
const execAsync = promisify(exec);

/**
 * Run WeasyPrint command to generate PDF
 */
async function generatePDF(inputFile: string, outputFile: string): Promise<void> {
    try {
        const weasyPrintPath = process.env.NODE_ENV === 'production' ? 'weasyprint' : '/opt/homebrew/bin/weasyprint';
        const { stdout, stderr } = await execAsync(
            `${weasyPrintPath} "${inputFile}" "${outputFile}" --quiet --optimize-images --pdf-variant pdf/ua-1`
        );
        
        // Check if the output file exists and has size
        const stats = await fs.stat(outputFile);
        if (stats.size === 0) {
            throw new Error('Generated PDF is empty');
        }
        
        if (stderr) {
            console.error('WeasyPrint stderr:', stderr);
        }
        if (stdout) {
            console.log('WeasyPrint stdout:', stdout);
        }
    } catch (error) {
        console.error('WeasyPrint error:', error);
        throw error;
    }
}

/**
 * Generate QR code SVG for a URL
 */
function generateQRCode(url: string): string {
    const qr = new QRCode({
        content: url,
        width:80,
        height: 80,
        color: "#000000",
        background: "#ffffff",
        ecl: "M"
    });
    return qr.svg();
}

/**
 * Generate HTML for an insight
 */
function createInsightHtml(insight: any): string {
    const insightData: InsightType = {
        type: insight.definition.type,
        data: insight.result,
        definition: insight.definition
    };

    let contentHtml;
    try {
        contentHtml = renderInsight(insightData);
    } catch (error) {
        console.error('Error rendering insight:', error);
        // Fallback to basic rendering if no renderer found
        contentHtml = `<pre>${JSON.stringify(insight.result, null, 2)}</pre>`;
    }

    return `
        <div class="insight">
            <div class="insight-header">
                <span class="insight-type">${insight.definition.type.toUpperCase()}</span>
            </div>
            <div class="insight-content">
                ${contentHtml}
            </div>
        </div>
    `;
}

/**
 * Generate a PDF with insights from a conversation
 */
export const generateInsightsPdf = responseWrapper(async (req: AuthRequest, res: Response): Promise<void> => {
    const organizationId = req.user?.organization?.id;
    if (!organizationId) {
        throw new Error('Unauthorized');
    }

    const conversationId = parseInt(req.params.id);
    if (isNaN(conversationId)) {
        throw new Error('Invalid conversation ID');
    }

    // Get conversation with basic info
    const conversation = await AppDataSource
        .getRepository('Conversation')
        .createQueryBuilder('conversation')
        .leftJoinAndSelect('conversation.team', 'team')
        .leftJoinAndSelect('conversation.contact', 'contact')
        .leftJoinAndSelect('conversation.owner', 'owner')
        .leftJoinAndSelect('conversation.organization', 'organization')
        .where('conversation.id = :id', { id: conversationId })
        .andWhere('conversation.organizationId = :organizationId', { organizationId })
        .getOne();

    if (!conversation) {
        throw new Error('Not found');
    }

    // Get insights with definitions and projects
    const insights = await AppDataSource
        .getRepository('Insight')
        .createQueryBuilder('insight')
        .leftJoinAndSelect('insight.definition', 'definition')
        .leftJoinAndSelect('definition.projectInsightDefinitions', 'pid')
        .leftJoinAndSelect('pid.project', 'project')
        .where('insight.conversationId = :conversationId', { conversationId })
        .orderBy('project.name', 'ASC')
        .addOrderBy('pid.order', 'ASC')
        .addOrderBy('definition.name', 'ASC')
        .getMany();

    // Group insights by project
    const insightsByProject = insights.reduce<Record<string, any[]>>((acc, insight) => {
        const projectName = insight.definition?.projectInsightDefinitions?.[0]?.project?.name || 'Other';
        if (!acc[projectName]) {
            acc[projectName] = [];
        }
        acc[projectName].push(insight);
        return acc;
    }, {});

    // Generate HTML
    const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                @page {
                    margin: 40px;
                    size: A4;
                    @top-right {
                        content: counter(page) " of " counter(pages);
                        font-size: 10px;
                        color: #6B7280;
                    }
                }
                body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                    line-height: 1.4;
                    color: #111827;
                    margin: 0;
                    padding: 0;
                }
                .logo {
                    width: 40px;
                    margin-bottom: 20px;
                }
                .organization {
                    font-size: 12px;
                    margin-bottom: 20px;
                }
                h1 {
                    font-size: 16px;
                    font-weight: bold;
                    margin: 0 0 20px 0;
                }
                h2 {
                    font-size: 12px;
                    font-weight: bold;
                    margin: 0 0 10px 0;
                }
                .details {
                    font-size: 12px;
                    margin-bottom: 30px;
                }
                .project-title {
                    font-size: 26px;
                    font-weight: bold;
                    margin: 0 0 15px 0;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #E5E7EB;
                }
                .insight {
                    background: #F8F7F6;
                    border-radius: 4px;
                    padding: 0px;
                    margin-bottom: 15px;
                    border: 1px solid #e9e8e7;
                }
                .insight:last-child {
                    margin-bottom: 0;
                }
                .insight-title {
                    font-size: 12px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .insight-content {
                    font-size: 12px;
                    background: #fff;
                    padding: 15px;
                }

                .insight-content h2 {
                    font-size: 12px;
                    font-weight: bold;
                    margin: 0 0 10px 0;
                }
                .project-section {
                    margin-bottom: 30px;
                    // break-inside: avoid;
                }

                .insight-header {
                    font-size: 8px;

                    padding: 5px 7px;
                }

                .project-section h2 {
                    font-size: 14px;
                    font-weight: 400;
                }

                .project-section:last-child {
                    margin-bottom: 0;
                }

                .header-container {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 20px;
                    margin-right: -40px;
                }

                .header-left {
                    display: flex;
                    align-items: flex-start;
                    flex: 1;
                }

                .qr-code {
                    width: 80px;
                    height: 80px;
                    margin-left: 20px;
                    margin-right: 40px;
                }

                .qr-code-link {
                    display: block;
                    text-decoration: none;
                    cursor: pointer;
                    transition: opacity 0.2s;
                }

                .qr-code-link:hover {
                    opacity: 0.8;
                }
            </style>
        </head>
        <body>
            <div class="header-container">
                <div class="header-left">
                    <img class="logo" src="data:image/svg+xml;base64,${Buffer.from(
                        '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">' +
                        '<path d="M0 3C0 1.34315 1.34315 0 3 0H6C7.65685 0 9 1.34315 9 3V8C9 8.55228 8.55228 9 8 9H3C1.34315 9 0 7.65685 0 6V3Z" fill="#F35100"/>' +
                        '<path d="M11 4.5C11 3.10218 11 2.40326 11.2284 1.85195C11.5328 1.11687 12.1169 0.532843 12.8519 0.228361C13.4033 0 14.1022 0 15.5 0V0C16.8978 0 17.5967 0 18.1481 0.228361C18.8831 0.532843 19.4672 1.11687 19.7716 1.85195C20 2.40326 20 3.10218 20 4.5V4.5C20 5.89782 20 6.59674 19.7716 7.14805C19.4672 7.88313 18.8831 8.46716 18.1481 8.77164C17.5967 9 16.8978 9 15.5 9H13C12.0572 9 11.5858 9 11.2929 8.70711C11 8.41421 11 7.94281 11 7V4.5Z" fill="#F35100"/>' +
                        '<path d="M0 14C0 12.3431 1.34315 11 3 11H8C8.55228 11 9 11.4477 9 12V17C9 18.6569 7.65685 20 6 20H3C1.34315 20 0 18.6569 0 17V14Z" fill="#F35100"/>' +
                        '<path d="M11 12C11 11.4477 11.4477 11 12 11H17C18.6569 11 20 12.3431 20 14V17C20 18.6569 18.6569 20 17 20H14C12.3431 20 11 18.6569 11 17V12Z" fill="#F35100"/>' +
                        '</svg>'
                    ).toString('base64')}" style="margin-right: 10px;">
                    <p style="font-size: 10px; max-width: 400px; margin: 0; color:#999999">
                        This document contains Luno-generated insights that may include confidential information. 
                        By accessing it, you confirm having all necessary permissions to view or share this content. 
                        Unauthorized use is prohibited and may violate applicable laws.
                    </p>
                </div>
                <div class="qr-code">
                    <a href="https://meetluno.com/app/conversations/${conversationId}" class="qr-code-link" target="_blank">
                        ${generateQRCode(`https://meetluno.com/app/conversations/${conversationId}`)}
                    </a>
                </div>
            </div>
            ${conversation.organization?.name ? `
                <div class="organization">${conversation.organization.name}</div>
            ` : ''}
            <h1>Conversation #${conversationId}</h1>
            <h2>Conversation Details:</h2>
            <div class="details">
                Team: ${conversation.team?.name || 'N/A'}<br>
                Date: ${new Date(conversation.created_at).toLocaleString()}<br>
                Medium: ${conversation.medium}<br>
                Contact: ${conversation.contact?.name || 'N/A'}<br>
                Owner: ${conversation.owner?.name || 'N/A'}
            </div>
            ${Object.entries(insightsByProject).map(([projectName, projectInsights]) => `
                <div class="project-section">
                    <h2>${projectName}</h2>
                    ${projectInsights.map(insight => createInsightHtml(insight)).join('\n')}
                </div>
            `).join('\n')}
        </body>
        </html>
    `;

    // Log generated HTML content
    console.log('Generated HTML:', html);

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=conversation-${conversationId}-insights.pdf`);

    try {
        // Create temp directory for HTML and PDF files
        const tempDir = path.join(os.tmpdir(), `pdf-${Date.now()}`);
        await fs.ensureDir(tempDir);

        // Write HTML to temp file
        const htmlFile = path.join(tempDir, 'input.html');
        await fs.writeFile(htmlFile, html, 'utf8');

        // Generate PDF using WeasyPrint
        const pdfFile = path.join(tempDir, 'output.pdf');
        await generatePDF(htmlFile, pdfFile);

        // Read the generated PDF and check its size
        const pdfBuffer = await fs.readFile(pdfFile);
        if (pdfBuffer.length === 0) {
            throw new Error('PDF buffer is empty');
        }
        console.log('Generated PDF size:', pdfBuffer.length, 'bytes');

        // Clean up temp files
        await fs.remove(tempDir);

        // Send the PDF
        res.send(pdfBuffer);
    } catch (error) {
        console.error('PDF generation error:', error);
        throw error;
    }
});
