import { Response } from 'express';
import { AppDataSource } from '../../data-source';
import { Conversation } from '../../models/conversation';
import { AuthRequest } from '../../middleware/auth.middleware';
import { Between } from 'typeorm';
import { BaseController } from '../base.controller';
import { hasPermission } from '../../decorators/permission.decorator';
import { throwAPIError } from '../../middleware/error-handler.middleware';

/**
 * Controller for conversation statistics
 */
export class ConversationStatsController extends BaseController {
  private conversations = AppDataSource.getRepository(Conversation);

  constructor() {
    super(); // Call BaseController constructor for auto-wrapping
  }
  /**
   * Get daily conversation counts for a organization within a date range
   * @param req Express request with authenticated user
   * @param res Express response
   * @param next Express next function
   */
  @hasPermission('conversations:read:own')
  public async getConversationStats(req: AuthRequest, res: Response): Promise<void> {
    const { startDate, endDate } = req.query;

    // Default to last 30 days if no dates provided
    const end = endDate ? new Date(endDate as string) : new Date();
    const start = startDate ? new Date(startDate as string) : new Date(end);
    start.setDate(end.getDate() - 30);

    // Ensure dates are at the start/end of their respective days
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);

    // Get conversations grouped by date and team
    const stats = await this.conversations
      .createQueryBuilder('conversation')
      .leftJoin('conversation.team', 'team')
      .select([
        'DATE(conversation.created_at) as date',
        'COALESCE(team.id, 0) as "teamId"',
        'COALESCE(team.name, \'Unassigned\') as "teamName"',
        'COALESCE(team.color, \'#808080\') as "teamColor"',
        'COUNT(conversation.id)::integer as count'
      ])
      .where('conversation.organizationId = :organizationId', { organizationId: this.organizationId })
      .andWhere('conversation.created_at BETWEEN :start AND :end', { start, end })
      .groupBy('DATE(conversation.created_at), team.id, team.name, team.color')
      .orderBy('date', 'ASC')
      .getRawMany();

    res.json(stats.map(stat => ({
      ...stat,
      count: parseInt(stat.count, 10) // Ensure count is a number
    })));
  }

  /**
   * Get conversation counts by medium for a organization within a date range
   * @param req Express request with authenticated user
   * @param res Express response
   * @param next Express next function
   */
  @hasPermission('conversations:read:own')
  public async getConversationMediumStats(req: AuthRequest, res: Response): Promise<void> {
    const { days = 30 } = req.query; // Default to 30 days

    // Use UTC dates to match database
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - Number(days));

    // Set to start/end of day in UTC
    start.setUTCHours(0, 0, 0, 0);
    end.setUTCHours(23, 59, 59, 999);

    // Get conversations grouped by medium
    const query = this.conversations
      .createQueryBuilder('conversation')
      .select([
        'conversation.medium as medium',
        'COUNT(conversation.id)::integer as total',
        'ARRAY_AGG(conversation.created_at ORDER BY conversation.created_at) as dates'
      ])
      .where('conversation."organizationId" = :organizationId', { organizationId: this.organizationId })
      .andWhere('conversation.created_at >= :start', { start })
      .andWhere('conversation.created_at <= :end', { end })
      .andWhere('conversation.medium IS NOT NULL')
      .groupBy('conversation.medium');

    // Log the raw query and parameters
    const [rawQuery, parameters] = query.getQueryAndParameters();
    const stats = await query.getRawMany();

    // Map the medium types to display names
    const mediumDisplayNames = {
      'call': 'Phone',
      'video': 'Video',
      'chat': 'Chat'
    };

    // Transform stats to include daily counts
    const mediumStats = stats.map(stat => {
      const dates = stat.dates.map((d: string) => new Date(d));
      return {
        medium: mediumDisplayNames[stat.medium as keyof typeof mediumDisplayNames] || stat.medium,
        total: stat.total,
        dates,
        counts: Array(Number(days)).fill(0).map((_, i) => {
          const dayStart = new Date(end);
          dayStart.setDate(dayStart.getDate() - (Number(days) - 1) + i);
          dayStart.setUTCHours(0, 0, 0, 0);
          
          const dayEnd = new Date(dayStart);
          dayEnd.setUTCHours(23, 59, 59, 999);
          
          return dates.filter(d => d >= dayStart && d <= dayEnd).length;
        })
      };
    });

    // Add default entries for mediums with no data
    const existingMediums = mediumStats.map(s => s.medium);
    Object.values(mediumDisplayNames).forEach(displayName => {
      if (!existingMediums.includes(displayName)) {
        mediumStats.push({
          medium: displayName,
          total: 0,
          dates: [],
          counts: Array(Number(days)).fill(0)
        });
      }
    });

    res.json(mediumStats);
  }
}

// Create a singleton instance for export
export const conversationStatsController = new ConversationStatsController();
