import { Response } from 'express';
import { randomBytes, createHmac } from 'crypto';
import { AppDataSource } from '../../data-source';
import { ConversationSource } from '../../models/conversationsource';
import type { AuthenticatedRequest } from '../../types/express';
import { hasPermission } from '../../decorators/permission.decorator';
import { audited } from '../../decorators/audit.decorator';
import { WebhookProcessorFactory } from '../../services/conversation/webhooks/webhook-processor-factory';
import { ZoomWebhookProcessor } from '../../services/conversation/webhooks/zoom-webhook-processor';
import { OpenPhoneWebhookProcessor } from '../../services/conversation/webhooks/openphone-webhook-processor';

// Register webhook processors
WebhookProcessorFactory.registerProcessor('zoom', ZoomWebhookProcessor);
WebhookProcessorFactory.registerProcessor('openphone', OpenPhoneWebhookProcessor);

/**
 * Controller for managing conversation source webhooks
 * @class
 * @description Handles operations related to conversation source webhooks, including creation, invalidation, and regeneration
 */
export class WebhookController {
    /**
     * Creates a webhook for a conversation source
     * @param {AuthenticatedRequest} req - The authenticated request object containing the source ID
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the webhook is created
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'create',
        resource: 'conversation-source-webhook',
        includeRequest: true,
        includeResponse: false
    })
    public async createWebhook(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const sourceId = parseInt(id, 10);

            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }

            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: {
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });

            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }

            // Generate webhook ID and secret
            const webhookId = randomBytes(16).toString('hex');
            const webhookSecret = randomBytes(32).toString('hex');

            // Update the source with webhook information
            source.webhookId = webhookId;
            source.webhookSecret = webhookSecret;
            source.webhookStatus = 'active';

            await repository.save(source);

            // Construct the full webhook URL for the response
            const baseUrl = process.env.VITE_MOTHERSHIP_URL || 'https://mothership.meetluno.com';
            const webhookUrl = `${baseUrl}/api/webhooks/${source.type}/${webhookId}`;

            res.json({
                success: true,
                message: 'Webhook created successfully',
                webhook: {
                    url: webhookUrl,
                    secret: webhookSecret,
                    status: 'active'
                }
            });
        } catch (error) {
            console.error('Error creating webhook:', error);
            res.status(500).json({
                error: 'Failed to create webhook',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Invalidates a webhook for a conversation source
     * @param {AuthenticatedRequest} req - The authenticated request object containing the source ID
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the webhook is invalidated
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'update',
        resource: 'conversation-source-webhook',
        includeRequest: true,
        includeResponse: false
    })
    public async invalidateWebhook(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const sourceId = parseInt(id, 10);

            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }

            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: {
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });

            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }

            // Check if the source has a webhook
            if (!source.webhookId || !source.webhookSecret) {
                res.status(400).json({ error: 'No webhook exists for this source' });
                return;
            }

            // Invalidate the webhook
            source.webhookStatus = 'inactive';

            await repository.save(source);

            res.json({
                success: true,
                message: 'Webhook invalidated successfully',
                webhookStatus: 'inactive'
            });
        } catch (error) {
            console.error('Error invalidating webhook:', error);
            res.status(500).json({
                error: 'Failed to invalidate webhook',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Regenerates a webhook for a conversation source
     * @param {AuthenticatedRequest} req - The authenticated request object containing the source ID
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the webhook is regenerated
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'update',
        resource: 'conversation-source-webhook',
        includeRequest: true,
        includeResponse: false
    })
    public async regenerateWebhook(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const sourceId = parseInt(id, 10);

            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }

            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: {
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });

            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }

            // Check if the source has a webhook
            if (!source.webhookId || !source.webhookSecret) {
                res.status(400).json({ error: 'No webhook exists for this source' });
                return;
            }

            // Generate new webhook ID and secret
            const webhookId = randomBytes(16).toString('hex');
            const webhookSecret = randomBytes(32).toString('hex');

            // Update the source with new webhook information
            source.webhookId = webhookId;
            source.webhookSecret = webhookSecret;

            await repository.save(source);

            // Construct the full webhook URL for the response
            const baseUrl = process.env.VITE_MOTHERSHIP_URL || 'https://not-configured.meetluno.com';
            const webhookUrl = `${baseUrl}/api/webhooks/${source.type}/${webhookId}`;

            res.json({
                success: true,
                message: 'Webhook regenerated successfully',
                webhook: {
                    url: webhookUrl,
                    secret: webhookSecret,
                    status: source.webhookStatus
                }
            });
        } catch (error) {
            console.error('Error regenerating webhook:', error);
            res.status(500).json({
                error: 'Failed to regenerate webhook',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Updates the webhook secret for a conversation source
     * @param {AuthenticatedRequest} req - The authenticated request object containing the source ID and the new secret
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the webhook secret is updated
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'update',
        resource: 'conversation-source-webhook',
        includeRequest: false, // Don't log the secret in the audit
        includeResponse: false
    })
    public async updateWebhookSecret(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const { secret } = req.body;
            const sourceId = parseInt(id, 10);

            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }

            if (!secret || typeof secret !== 'string') {
                res.status(400).json({ error: 'Webhook secret is required and must be a string' });
                return;
            }

            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: {
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });

            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }

            // Check if the source has a webhook
            if (!source.webhookId) {
                res.status(400).json({ error: 'No webhook exists for this source' });
                return;
            }

            // Update the webhook secret
            source.webhookSecret = secret;

            await repository.save(source);

            res.json({
                success: true,
                message: 'Webhook secret updated successfully'
            });
        } catch (error) {
            console.error('Error updating webhook secret:', error);
            res.status(500).json({
                error: 'Failed to update webhook secret',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Processes an incoming webhook request
     * @param {Request} req - The request object containing the webhook data
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the webhook is processed
     */
    public async processWebhook(req: { params: { webhookId: string, source: string }, headers: Record<string, string>, body: Record<string, unknown> }, res: Response): Promise<void> {
        try {
            console.log('Processing webhook:', {
                params: req.params,
                headers: Object.keys(req.headers),
                body: req.body
            });

            const { source, webhookId } = req.params;

            if (!webhookId) {
                console.error('Missing webhook ID in request path');
                res.status(400).json({ error: 'Missing webhook ID' });
                return;
            }

            // Find the corresponding conversation source
            const repository = AppDataSource.getRepository(ConversationSource);
            const conversationSource = await repository.findOne({
                where: {
                    webhookId,
                    type: source
                }
            });

            if (!conversationSource) {
                console.error(`No source found for webhook ID: ${webhookId}`);
                res.status(404).json({ error: 'Webhook not found' });
                return;
            }

            // Check if the webhook is active
            if (conversationSource.webhookStatus !== 'active') {
                console.error(`Webhook is inactive for source: ${conversationSource.id}`);
                res.status(403).json({ error: 'Webhook is inactive' });
                return;
            }

            // Get the processor for this source type
            const processor = WebhookProcessorFactory.createProcessor(conversationSource);

            if (!processor) {
                console.error(`No processor found for source type: ${conversationSource.type}`);
                res.status(400).json({ error: `Unsupported source type: ${conversationSource.type}` });
                return;
            }

            // Process the webhook
            // Extract signature from headers if it exists
            const signature = req.headers['x-webhook-signature'] || '';

            // Verify the signature
            const isValid = processor.verifySignature(req.body, signature as string, conversationSource.webhookSecret);

            if (!isValid) {
                console.error('Invalid webhook signature');
                res.status(403).json({ error: 'Invalid signature' });
                return;
            }

            const result = await processor.processWebhook(req.body, req.headers);

            res.status(200).json({ success: true, processed: result });
        } catch (error) {
            console.error('Error processing webhook:', error);
            res.status(500).json({ error: 'Failed to process webhook' });
        }
    }
}

// Create a singleton instance for export
export const webhookController = new WebhookController();
