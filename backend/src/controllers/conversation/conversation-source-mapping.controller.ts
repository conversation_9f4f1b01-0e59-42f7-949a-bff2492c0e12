import { Request, Response } from "express";
import { AppDataSource } from "../../data-source";
import { ConversationSourceMapping } from "../../models/conversation-source-mapping";
import { User } from "../../models/user";
import { RoleService } from "../../services/role.service";
import { Organization } from "../../models/organization";
import { ConversationSource } from "../../models/conversationsource";
import { SourceFactory } from "../../services/conversation/sources/source-factory";

// Define extended request interface with user and organization
interface AuthenticatedRequest extends Request {
  user?: User;
  organization?: Organization;
}

/**
 * Controller for managing conversation source mappings
 */
export class ConversationSourceMappingController {
  private repository = AppDataSource.getRepository(ConversationSourceMapping);
  private userRepository = AppDataSource.getRepository(User);
  private sourceRepository = AppDataSource.getRepository(ConversationSource);
  private roleService = new RoleService();
  
  /**
   * Trigger a full sync for a specific conversation source
   * Requires 'conversation-sources:write' permission with appropriate scope
   */
  async triggerFullSync(req: AuthenticatedRequest, res: Response) {
    try {
      const sourceId = parseInt(req.params.id);
      const user = req.user;
      const organizationId = req.organization?.id;
      const { forceSync, startDate, endDate } = req.body;

      if (!user || !user.roleId || !organizationId) {
        return res.status(401).json({ error: "Authentication required" });
      }

      if (isNaN(sourceId)) {
        return res.status(400).json({ error: "Invalid source ID" });
      }

      // Check permission
      const hasPermission = await this.roleService.hasPermission(
        user.roleId,
        "conversation-sources", // resource
        "write", // action
        "all" // requiredScope
      );

      if (!hasPermission) {
        return res.status(403).json({ error: "Permission denied" });
      }

      // Find the source
      const source = await this.sourceRepository.findOne({
        where: { id: sourceId, organizationId }
      });

      if (!source) {
        return res.status(404).json({ error: "Source not found" });
      }

      // Parse dates if provided
      const options: any = { forceSync: !!forceSync };
      if (startDate) options.startDate = new Date(startDate);
      if (endDate) options.endDate = new Date(endDate);

      // Use the factory to create source service and trigger full sync
      const sourceService = SourceFactory.createSourceService(source.type, source.credentials);
      
      // Make sure the source service implements fullSync
      if (!sourceService.fullSync) {
        return res.status(501).json({ 
          error: `Full sync not implemented for source type: ${source.type}` 
        });
      }

      // Start the sync process
      // For long-running operations, we might want to make this async in the future
      const result = await sourceService.fullSync(sourceId, options);

      return res.status(200).json(result);
    } catch (error) {
      console.error("Error triggering full sync:", error);
      return res.status(500).json({ 
        error: "Failed to trigger full sync",
        details: error.message 
      });
    }
  }

  /**
   * Get all mappings for a specific conversation source
   * Requires 'conversation-sources:read' permission with appropriate scope
   */
  async getMappingsForSource(req: AuthenticatedRequest, res: Response) {
    try {
      const sourceId = parseInt(req.params.id);
      const user = req.user;
      const organizationId = req.organization?.id;

      if (!user || !user.roleId || !organizationId) {
        return res.status(401).json({ error: "Authentication required" });
      }

      if (isNaN(sourceId)) {
        return res.status(400).json({ error: "Invalid source ID" });
      }

      // Fetch the mappings
      const mappings = await this.repository.find({
        where: { 
          conversationSourceId: sourceId,
          organizationId
        },
        relations: ["user"]
      });

      // Transform the data to return only needed fields
      const transformedMappings = mappings.map(mapping => ({
        id: mapping.id,
        userId: mapping.userId,
        user: mapping.user ? {
          id: mapping.user.id,
          name: mapping.user.name,
          email: mapping.user.email
        } : null,
        externalId: mapping.externalId,
        externalMetadata: mapping.externalMetadata,
        createdAt: mapping.createdAt,
        updatedAt: mapping.updatedAt
      }));

      return res.status(200).json({ 
        mappings: transformedMappings 
      });
    } catch (error) {
      console.error("Error fetching mappings:", error);
      return res.status(500).json({ 
        error: "Failed to fetch user mappings" 
      });
    }
  }

  /**
   * Create a mapping between an internal user and external user
   * Requires 'conversation-sources:write' permission with appropriate scope
   */
  async createMapping(req: AuthenticatedRequest, res: Response) {
    try {
      const sourceId = parseInt(req.params.id);
      const { userId, externalId, externalMetadata } = req.body;
      const currentUser = req.user;
      const organizationId = req.organization?.id;

      if (!currentUser || !currentUser.roleId || !organizationId) {
        return res.status(401).json({ error: "Authentication required" });
      }

      if (isNaN(sourceId) || !userId || !externalId) {
        return res.status(400).json({ error: "Invalid input data" });
      }

      // Check if user exists in the organization
      const user = await this.userRepository.findOne({
        where: { id: userId, organizationId }
      });

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Check if source exists
      const source = await this.sourceRepository.findOne({
        where: { id: sourceId }
      });

      if (!source) {
        return res.status(404).json({ error: "Source not found" });
      }

      // Check if mapping already exists
      const existingMapping = await this.repository.findOne({
        where: {
          userId,
          conversationSourceId: sourceId,
          organizationId
        }
      });

      if (existingMapping) {
        return res.status(409).json({ error: "Mapping already exists for this user" });
      }

      // Create the mapping
      const mapping = new ConversationSourceMapping();
      mapping.userId = userId;
      mapping.conversationSourceId = sourceId;
      mapping.organizationId = organizationId;
      mapping.externalId = externalId;
      mapping.externalMetadata = externalMetadata || {};

      await this.repository.save(mapping);

      // Fetch the user details for the response
      const savedMapping = await this.repository.findOne({
        where: { id: mapping.id },
        relations: ["user"]
      });

      return res.status(201).json({
        mapping: {
          id: savedMapping.id,
          userId: savedMapping.userId,
          user: savedMapping.user ? {
            id: savedMapping.user.id,
            name: savedMapping.user.name,
            email: savedMapping.user.email
          } : null,
          externalId: savedMapping.externalId,
          externalMetadata: savedMapping.externalMetadata,
          createdAt: savedMapping.createdAt,
          updatedAt: savedMapping.updatedAt
        }
      });
    } catch (error) {
      console.error("Error creating mapping:", error);
      return res.status(500).json({ 
        error: "Failed to create user mapping" 
      });
    }
  }

  /**
   * Get external users from a specific conversation source
   * Requires 'conversation-sources:read' permission with appropriate scope
   */
  async getExternalUsersFromSource(req: AuthenticatedRequest, res: Response) {
    try {
      const sourceId = parseInt(req.params.id);
      const user = req.user;
      const organizationId = req.organization?.id;

      if (!user || !user.roleId || !organizationId) {
        return res.status(401).json({ error: "Authentication required" });
      }

      if (isNaN(sourceId)) {
        return res.status(400).json({ error: "Invalid source ID" });
      }

      // Fetch the source to get its type and credentials
      const source = await this.sourceRepository.findOneBy({ id: sourceId });
      
      if (!source) {
        return res.status(404).json({ error: "Source not found" });
      }

      try {
        // Use the factory to create an appropriate source service
        const sourceService = SourceFactory.createSourceService(source.type, source.credentials);
        
        // Call the listUsers method on the source service
        const externalUsers = await sourceService.listUsers();

        return res.status(200).json({ 
          externalUsers 
        });
      } catch (error) {
        if (error instanceof Error) {
          return res.status(400).json({ 
            error: error.message
          });
        }
        throw error;
      }
    } catch (error) {
      console.error("Error fetching external users:", error);
      return res.status(500).json({ 
        error: "Failed to fetch external users" 
      });
    }
  }

  /**
   * Delete a specific mapping by ID
   * Requires 'conversation-sources:write' permission with appropriate scope
   */
  async deleteMapping(req: AuthenticatedRequest, res: Response) {
    try {
      const mappingId = parseInt(req.params.mappingId);
      const user = req.user;
      const organizationId = req.organization?.id;

      if (!user || !user.roleId || !organizationId) {
        return res.status(401).json({ error: "Authentication required" });
      }

      if (isNaN(mappingId)) {
        return res.status(400).json({ error: "Invalid mapping ID" });
      }

      // Check if mapping exists and belongs to the organization
      const mapping = await this.repository.findOne({
        where: { 
          id: mappingId,
          organizationId
        }
      });

      if (!mapping) {
        return res.status(404).json({ error: "Mapping not found" });
      }

      // Delete the mapping
      await this.repository.remove(mapping);

      return res.status(200).json({ 
        success: true,
        message: "Mapping deleted successfully" 
      });
    } catch (error) {
      console.error("Error deleting mapping:", error);
      return res.status(500).json({ 
        error: "Failed to delete mapping" 
      });
    }
  }
}

export const conversationSourceMappingController = new ConversationSourceMappingController();
