import { Response } from 'express';
import { randomBytes, createHmac } from 'crypto';
import { AppDataSource } from '../../data-source';
import { ConversationSource } from '../../models/conversationsource';
import { OAuthState } from '../../models/oauthstate';
import { <PERSON>Than } from 'typeorm';
import type { AuthenticatedRequest } from '../../types/express';
import { adapterRegistry } from '../../services/conversation/sources/registry';
import { HubspotService } from '../../services/conversation/sources/hubspot/index';
import { ZoomService } from '../../services/conversation/sources/zoom/index'; // Fix ZoomService import to use the correct path
import { OpenPhoneService } from '../../services/conversation/sources/openphone/index';
import { TalkdeskService } from '../../services/conversation/sources/talkdesk/index';
import { hasPermission } from '../../decorators/permission.decorator';
import { audited } from '../../decorators/audit.decorator';

// Define source types directly
enum SourceType {
    TALKDESK = 'talkdesk',
    HUBSPOT = 'hubspot',
    ZOOM = 'zoom',
    OPENPHONE = 'openphone'
}

/**
 * Controller for managing conversation sources
 * @class
 * @description Handles operations related to conversation sources, including retrieval, creation, and management of source adapters
 */
export class ConversationSourceController {
    /**
     * Get all conversation sources for the current organization
     */
    @hasPermission('sources:read:all')
    @audited({
        action: 'read',
        resource: 'conversation-source',
        includeRequest: false,
        includeResponse: false
    })

    /**
     * Retrieves all conversation sources for the current organization
     * @param {AuthenticatedRequest} req - The authenticated request object containing the user's organization ID
     * @param {Response} res - The response object to send the conversation sources or error message
     * @returns {Promise<void>} - A promise that resolves when the conversation sources are retrieved or an error occurs
     */
    public async findAll(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const sources = await AppDataSource
                .getRepository(ConversationSource)
                .find({
                    where: { organizationId: req.user.organization.id },
                    order: { createdAt: 'DESC' }
                });

            // Map sources to remove sensitive data
            const sanitizedSources = sources.map(source => ({
                ...source,
                credentials: source.credentials ? { 
                    exists: true,
                    type: source.type
                } : null,
                // Include webhook data but strip out the secret for security
                webhookId: source.webhookId,
                webhookStatus: source.webhookStatus,
                // Don't return the actual webhook secret
                webhookSecret: source.webhookSecret ? true : false
            }));

            res.json(sanitizedSources);
        } catch (error) {
            console.error('Error fetching conversation sources:', error);
            res.status(500).json({ error: 'Failed to fetch conversation sources' });
        }
    }

    /**
     * Get a specific conversation source by ID
     * @param {AuthenticatedRequest} req - The authenticated request object containing the source ID
     * @param {Response} res - The response object 
     * @returns {Promise<void>}
     */
    @hasPermission('sources:read:all')
    @audited({
        action: 'read',
        resource: 'conversation-source',
        includeRequest: false,
        includeResponse: false
    })
    public async findOne(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const sourceId = parseInt(id, 10);
            
            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }
            
            const source = await AppDataSource
                .getRepository(ConversationSource)
                .findOne({
                    where: { 
                        id: sourceId,
                        organizationId: req.user.organization.id
                    }
                });
                
            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }
            
            // Don't return sensitive credentials in the API response
            const sourceWithoutSensitiveData = {
                ...source,
                credentials: source.credentials ? { 
                    // Just indicate that credentials exist, but don't return the actual values
                    exists: true,
                    type: source.type
                } : null,
                // Include webhook data but strip out the secret for security
                webhookId: source.webhookId,
                webhookStatus: source.webhookStatus,
                // Don't return the actual webhook secret
                webhookSecret: source.webhookSecret ? true : false
            };
            
            res.json(sourceWithoutSensitiveData);
        } catch (error) {
            console.error('Error fetching conversation source:', error);
            res.status(500).json({ error: 'Failed to fetch conversation source' });
        }
    }

    /**
     * Retrieves a specific conversation source adapter by ID
     * @param {AuthenticatedRequest} req - The authenticated request object containing the ID of the adapter to retrieve
     * @param {Response} res - The response object to send the adapter data or error message
     * @returns {Promise<void>} - A promise that resolves when the adapter is retrieved or an error occurs
     */
    @hasPermission('sources:read:all')
    public async findAdapter(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            
            // First check if the ID is numeric (a source ID)
            const numericId = parseInt(id, 10);
            
            if (!isNaN(numericId)) {
                // It's a numeric ID, look up the source first
                const source = await AppDataSource
                    .getRepository(ConversationSource)
                    .findOne({
                        where: { 
                            id: numericId,
                            organizationId: req.user.organization.id
                        }
                    });
                    
                if (!source) {
                    res.status(404).json({ error: 'Conversation source not found' });
                    return;
                }
                
                // Look up the adapter using the source's type
                const adapter = adapterRegistry[source.type];
                
                if (!adapter) {
                    res.status(404).json({ error: `Adapter for source type '${source.type}' not found` });
                    return;
                }
                
                // Return the adapter info and the source ID for reference
                res.json({
                    ...adapter,
                    sourceId: source.id,
                    sourceName: source.name,
                });
                return;
            }
            
            // It's a string ID, directly look up the adapter
            const adapter = adapterRegistry[id];
            
            if (!adapter) {
                res.status(404).json({ error: `Adapter '${id}' not found` });
                return;
            }

            res.json(adapter);
        } catch (error) {
            console.error('Error fetching adapter:', error);
            res.status(500).json({ error: 'Failed to fetch adapter' });
        }
    }

    /**
     * Retrieves a list of available conversation source adapters
     * @param {AuthenticatedRequest} _req - The authenticated request object
     * @param {Response} res - The response object to send the adapters or error message
     * @returns {Promise<void>} - A promise that resolves when the adapters are retrieved or an error occurs
     */
    @hasPermission('sources:read:all')
    public async findAllAdapters(_req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const adapters = Object.values(adapterRegistry);
            res.json(adapters);
        } catch (error) {
            console.error('Error fetching adapters:', error);
            res.status(500).json({ error: 'Failed to fetch adapters' });
        }
    }

    /**
     * Initiates the OAuth flow for a specific source
     * @param {AuthenticatedRequest} req - The authenticated request object containing the ID of the source
     * @param {Response} res - The response object to send the OAuth flow or error message
     * @returns {Promise<void>} - A promise that resolves when the OAuth flow is initiated or an error occurs
     */
    @hasPermission('sources:read:all')
    @audited({
        action: 'read',
        resource: 'conversation-source',
        includeRequest: false,
        includeResponse: false
    })
    public async initiateOAuth(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            // Add debug logging to see what's in the request body
            console.log('Request body in initiateOAuth:', req.body);
            const subdomain = req.body?.subdomain;
            console.log('Extracted subdomain:', subdomain);
            let source = null;
            const sourceId = parseInt(id, 10);
            const repository = AppDataSource.getRepository(ConversationSource);
            
            // Only attempt to find by ID if the parsed ID is a valid number
            if (!isNaN(sourceId)) {
                // Find the source by ID
                source = await repository.findOne({
                    where: { 
                        id: sourceId,
                        organizationId: req.user.organization.id 
                    }
                });
            }
            
            // If no source found with that ID, or if id wasn't a number, try looking up by type
            if (!source) {
                // Get service based on source type
                const service = this.getOAuthServiceForType(id);
                
                if (!service) {
                    res.status(400).json({ error: 'OAuth not supported for this source' });
                    return;
                }
                
                // Create or get the source record
                let existingSource = await repository.findOne({
                    where: {
                        type: id,
                        organizationId: req.user.organization.id,
                    },
                });

                if (!existingSource) {
                    existingSource = repository.create({
                        name: this.getDisplayNameForSourceType(id),
                        type: id,
                        organizationId: req.user.organization.id,
                        config: {
                            settings: {},
                            syncIntervalMinutes: 60,
                        },
                        isActive: true,
                        enabledFeatures: []
                    });
                    await repository.save(existingSource);
                    console.log('Created new source:', existingSource.id);
                }

                // Process OAuth for the source by type
                return this.processOAuth(existingSource, service, res, subdomain);
            }
            
            // Process OAuth for the source by ID
            // Get service based on the source type
            const service = this.getOAuthServiceForType(source.type);
            
            if (!service) {
                res.status(400).json({ error: 'OAuth not supported for this source type' });
                return;
            }
            
            return this.processOAuth(source, service, res, subdomain);
        } catch (error) {
            console.error('OAuth error:', error);
            res.status(500).json({ error: 'Failed to initiate OAuth flow' });
        }
    }

    /**
     * Get the appropriate OAuth service instance based on source type
     * @private
     * @param {string} sourceType The type of source (e.g., 'hubspot', 'zoom', 'openphone', 'talkdesk')
     * @param {ConversationSource} [source] Optional source object to pass to the service constructor
     * @returns {any} An instance of the appropriate service class or null if not supported
     */
    private getOAuthServiceForType(sourceType: string, source?: ConversationSource): any {
        console.log('Getting OAuth service for source type:', sourceType);
        if (source) {
            console.log('Using source with ID:', source.id);
        }
        
        switch (sourceType) {
            case SourceType.TALKDESK:
                // Pass source to TalkdeskService as it can use the credentials
                // The Talkdesk service needs the source credentials with subdomain
                return new TalkdeskService(source);
            case SourceType.HUBSPOT:
                // Check service constructor signature
                return new HubspotService();
            case SourceType.ZOOM:
                // Check service constructor signature
                return new ZoomService();
            case SourceType.OPENPHONE:
                // Check service constructor signature
                return new OpenPhoneService();
            default:
                return null;
        }
    }

    /**
     * Get a display name for a source type
     * @private
     * @param {string} sourceType The type of source
     * @returns {string} A user-friendly display name for the source type
     */
    private getDisplayNameForSourceType(sourceType: string): string {
        switch (sourceType) {
            case 'hubspot':
                return 'HubSpot';
            case 'zoom':
                return 'Zoom';
            case 'openphone':
                return 'OpenPhone';
            case 'talkdesk':
                return 'Talkdesk';
            default:
                return sourceType.charAt(0).toUpperCase() + sourceType.slice(1);
        }
    }

    /**
     * Process OAuth flow for a source
     * @private
     * @param {ConversationSource} source The source to process OAuth for
     * @param {any} service The OAuth service instance
     * @param {Response} res The response object
     * @param {string} [subdomain] Optional subdomain parameter for OAuth
     * @returns {Promise<void>}
     */
    private async processOAuth(source: ConversationSource, service: any, res: Response, subdomain?: string): Promise<void> {
        try {
            const oauthStateRepository = AppDataSource.getRepository(OAuthState);
            
            const state = randomBytes(32).toString('hex');
            
            // Store state in database instead of memory
            const oauthState = oauthStateRepository.create({
                state: state,
                sourceId: source.id.toString(),
                expiresAt: new Date(Date.now() + 3600000) // 1 hour expiration
            });
            await oauthStateRepository.save(oauthState);

            console.log('Generated state:', { state, sourceId: source.id });

            // Check if service has getAuthorizationUrl method
            if (typeof service.getAuthorizationUrl !== 'function') {
                throw new Error('OAuth service does not implement getAuthorizationUrl method');
            }
            
            // Important: Print the actual subdomain value being received
            console.log('Subdomain received by processOAuth:', subdomain);
            
            // Use the subdomain passed from initiateOAuth or from existing credentials
            const subdomainToUse = subdomain || source.credentials?.settings?.subdomain;
            console.log('Using subdomain for OAuth:', subdomainToUse);
            
            // Store subdomain in credentials if provided
            if (subdomainToUse) {
                // Initialize credentials and settings if they don't exist
                if (!source.credentials) {
                    source.credentials = { settings: { subdomain: subdomainToUse } };
                } else if (!source.credentials.settings) {
                    source.credentials.settings = { subdomain: subdomainToUse };
                } else {
                    source.credentials.settings.subdomain = subdomainToUse;
                }
                
                // Save the updated source with subdomain
                await AppDataSource.getRepository(ConversationSource).save(source);
                console.log('Saved subdomain to source credentials:', subdomainToUse);
            }

            // Pass subdomain directly to ensure it doesn't get lost
            const authUrl = service.getAuthorizationUrl(state, subdomainToUse);
            res.json({ authUrl });
        } catch (error) {
            console.error('Error in processOAuth:', error);
            res.status(500).json({ error: 'Failed to initiate OAuth process' });
        }
    }

    /**
     * Handle OAuth callback for a specific source
     * @param req Request object containing OAuth code and state
     * @param res Response object
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'update',
        resource: 'conversation-source-oauth',
        includeRequest: true,
        includeResponse: false
    })
    public async completeOAuth(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const { code, state } = req.body;

            console.log('OAuth callback:', { id, code, state });

            // Input validation
            if (!code || !state) {
                res.status(400).json({ error: 'Missing required parameters: code and state are required' });
                return;
            }

            // Get service based on source type
            const service = this.getOAuthServiceForType(id);
            
            if (!service) {
                res.status(400).json({ error: `Unsupported source type: ${id}` });
                return;
            }

            // Validate state and get sourceId from database
            const sourceId = await this.validateOAuthState(state);
            console.log('Found sourceId:', { state, sourceId });

            if (!sourceId) {
                res.status(400).json({ 
                    error: 'Invalid or expired state token. Please try authenticating again.',
                    code: 'INVALID_STATE'
                });
                return;
            }

            // Verify the source exists and belongs to the organization
            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: {
                    id: parseInt(sourceId, 10),
                    organizationId: req.user.organization.id
                }
            });

            if (!source) {
                res.status(404).json({ 
                    error: 'Conversation source not found or access denied',
                    code: 'SOURCE_NOT_FOUND'
                });
                return;
            }

            try {
                // Extract subdomain from source credentials if available
                const subdomain = source.credentials?.settings?.subdomain;
                console.log('Using subdomain for token exchange:', subdomain);
                
                // Exchange code for token with subdomain
                const credentials = await service.exchangeCodeForToken(code, subdomain);
                console.log('Got credentials:', { sourceId, hasAccessToken: !!credentials.access_token });
                
                await service.saveCredentials(sourceId, credentials);
                console.log('Saved credentials for source:', sourceId);

                res.json({ 
                    success: true,
                    message: 'OAuth flow completed successfully',
                    sourceId 
                });
            } catch (error) {
                console.error('Failed to exchange code for token:', error);
                res.status(500).json({ 
                    error: 'Failed to complete OAuth flow. Please try again.',
                    code: 'TOKEN_EXCHANGE_FAILED'
                });
            }
        } catch (error) {
            console.error('OAuth callback error:', error);
            res.status(500).json({ 
                error: 'An unexpected error occurred during OAuth callback',
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Validates an OAuth state token and returns the associated source ID
     * @param state The state token to validate
     * @returns The source ID if valid, null otherwise
     */
    private async validateOAuthState(state: string): Promise<string | null> {
        const oauthStateRepository = AppDataSource.getRepository(OAuthState);
        const oauthState = await oauthStateRepository.findOne({
            where: { 
                state,
                expiresAt: MoreThan(new Date())
            }
        });

        if (!oauthState) {
            return null;
        }

        // Clean up the used state
        await oauthStateRepository.remove(oauthState);
        
        return oauthState.sourceId;
    }

    /**
     * Creates or updates a conversation source with API key authentication
     * @param {AuthenticatedRequest} req - The authenticated request object containing source data
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the source is created/updated
     */
    @hasPermission('sources:create:all')
    @audited({
        action: 'create',
        resource: 'conversation-source',
        includeRequest: true,
        includeResponse: false
    })
    public async createOrUpdateSource(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            console.log('Request body:', req.body);
            const { type, apiKey, name } = req.body;
            
            if (!type) {
                res.status(400).json({ error: 'Source type is required' });
                return;
            }
            
            // Validate that the adapter exists
            const adapter = adapterRegistry[type];
            if (!adapter) {
                res.status(404).json({ error: `Adapter '${type}' not found` });
                return;
            }
            
            // Check if adapter uses API key auth
            if (adapter.authType !== 'apikey') {
                res.status(400).json({ 
                    error: `This source uses ${adapter.authType} authentication, not API key`,
                    code: 'INVALID_AUTH_TYPE'
                });
                return;
            }
            
            // API key validation logic based on the source type
            if (type === 'openphone') {
                // Only validate API key if one is provided
                if (apiKey) {
                    try {
                        const service = new OpenPhoneService();
                        const isValid = await service.validateConnection();
                        
                        if (!isValid) {
                            res.status(400).json({ 
                                error: 'Invalid API key',
                                code: 'INVALID_API_KEY'
                            });
                            return;
                        }
                    } catch (validationError) {
                        console.error('API key validation error:', validationError);
                        res.status(400).json({ 
                            error: 'Failed to validate API key',
                            details: validationError.message,
                            code: 'API_KEY_VALIDATION_ERROR'
                        });
                        return;
                    }
                }
            }
            // Add more source types here as needed
            
            // Create or update the source record
            const repository = AppDataSource.getRepository(ConversationSource);
            
            let source = await repository.findOne({
                where: {
                    type: type,
                    organizationId: req.user.organization.id,
                },
            });
            
            if (!source) {
                // Creating a new source - API key is required
                if (!apiKey) {
                    res.status(400).json({ error: 'API key is required for new sources' });
                    return;
                }
                
                source = repository.create({
                    name: name || adapter.name,
                    type: type,
                    organizationId: req.user.organization.id,
                    config: {
                        settings: {},
                        syncIntervalMinutes: 60,
                    },
                    isActive: true,
                    enabledFeatures: [],
                    webhookId: this.generateWebhookId(),
                    webhookSecret: this.generateWebhookSecret(),
                    webhookStatus: 'inactive'
                });
            } else {
                // Updating existing source
                // Update name if provided
                if (name) {
                    source.name = name;
                }
                
                // Generate webhook ID and secret if not already present
                if (!source.webhookId) {
                    source.webhookId = this.generateWebhookId();
                }
                
                if (!source.webhookSecret) {
                    source.webhookSecret = this.generateWebhookSecret();
                }
                
                // Save basic source data
                await repository.save(source);
            }
            
            // Save the source to get an ID if it's new
            await repository.save(source);
            
            // Update credentials based on source type only if apiKey is provided
            if (type === 'openphone' && apiKey) {
                const service = new OpenPhoneService();
                await service.saveCredentials(source.id, { apiKey });
            }
            // Add more source types here as needed
            
            res.status(201).json({
                success: true,
                message: 'Source configured successfully',
                sourceId: source.id
            });
        } catch (error) {
            console.error('Error creating/updating source:', error);
            res.status(500).json({ 
                error: 'Failed to create or update source',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Updates an existing conversation source
     * @param {AuthenticatedRequest} req - The authenticated request object containing update data
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the source is updated
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'update',
        resource: 'conversation-source',
        includeRequest: true,
        includeResponse: false
    })
    public async updateSource(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            console.log('Update source request:', { 
                params: req.params,
                body: req.body
            });
            
            const { id } = req.params;
            const { name, apiKey } = req.body;
            
            // Parse ID to number
            const sourceId = parseInt(id, 10);
            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }
            
            // Find the source
            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: {
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });
            
            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }
            
            // Get adapter info
            const adapter = adapterRegistry[source.type];
            if (!adapter) {
                res.status(404).json({ error: `Adapter for source type '${source.type}' not found` });
                return;
            }
            
            // Update name if provided
            if (name) {
                source.name = name;
            }
            
            // Validate and update API key if provided
            if (apiKey) {
                // API key validation logic based on the source type
                if (source.type === 'openphone') {
                    try {
                        const service = new OpenPhoneService();
                        const isValid = await service.validateConnection();
                        
                        if (!isValid) {
                            res.status(400).json({ 
                                error: 'Invalid API key',
                                code: 'INVALID_API_KEY'
                            });
                            return;
                        }
                        
                        // Update the API key
                        await service.saveCredentials(source.id, { apiKey });
                    } catch (validationError) {
                        console.error('API key validation error:', validationError);
                        res.status(400).json({ 
                            error: 'Failed to validate API key',
                            details: validationError.message,
                            code: 'API_KEY_VALIDATION_ERROR'
                        });
                        return;
                    }
                }
                // Add more source types here as needed
            }
            
            // Save basic source data
            await repository.save(source);
            
            res.json({
                success: true,
                message: 'Source updated successfully',
                sourceId: source.id
            });
        } catch (error) {
            console.error('Error updating source:', error);
            res.status(500).json({ 
                error: 'Failed to update source',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Update source settings
     * @param {AuthenticatedRequest} req - The authenticated request object containing the source ID and settings
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the settings are updated
     */
    @hasPermission('sources:update:all')
    @audited({
        action: 'update',
        resource: 'conversation-source-settings',
        includeRequest: true,
        includeResponse: false
    })
    public async updateSettings(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const { preferredMedium } = req.body;
            const sourceId = parseInt(id, 10);
            
            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }
            
            // Validate preferredMedium value
            const validMediumValues = ['text', 'video_audio'];
            if (preferredMedium && !validMediumValues.includes(preferredMedium)) {
                res.status(400).json({ 
                    error: 'Invalid preferredMedium value',
                    validValues: validMediumValues
                });
                return;
            }
            
            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: { 
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });
            
            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }
            
            // Update settings
            if (preferredMedium) {
                source.preferredMedium = preferredMedium as any;
            }
            
            await repository.save(source);
            
            res.json({
                success: true,
                message: 'Settings updated successfully',
                preferredMedium: source.preferredMedium
            });
        } catch (error) {
            console.error('Error updating source settings:', error);
            res.status(500).json({ 
                error: 'Failed to update source settings',
                details: error.message,
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Generates a random webhook ID
     * @returns {string} A random webhook ID
     * @private
     */
    private generateWebhookId(): string {
        return randomBytes(16).toString('hex');
    }

    /**
     * Generates a random webhook secret for signature verification
     * @returns {string} A random webhook secret
     * @private
     */
    private generateWebhookSecret(): string {
        return randomBytes(32).toString('hex');
    }

    /**
     * Triggers a full sync operation for a conversation source
     * @param {AuthenticatedRequest} req - The authenticated request containing the source ID and sync options
     * @param {Response} res - The response object
     * @returns {Promise<void>} - A promise that resolves when the sync operation is complete
     */
    @hasPermission('sources:read:all')
    @audited({
        action: 'update',
        resource: 'conversation-source-sync',
        includeRequest: true,
        includeResponse: false
    })
    public async fullSync(req: AuthenticatedRequest, res: Response): Promise<void> {
        try {
            const { id } = req.params;
            const { forceSync = false } = req.body;
            const sourceId = parseInt(id, 10);
            
            if (isNaN(sourceId)) {
                res.status(400).json({ error: 'Invalid source ID' });
                return;
            }
            
            const repository = AppDataSource.getRepository(ConversationSource);
            const source = await repository.findOne({
                where: { 
                    id: sourceId,
                    organizationId: req.user.organization.id
                }
            });
            
            if (!source) {
                res.status(404).json({ error: 'Conversation source not found' });
                return;
            }
            
            // Execute the full sync based on the source type
            let syncResult;
            
            switch (source.type) {
                case SourceType.TALKDESK: {
                    const talkdeskService = new TalkdeskService(source);
                    syncResult = await talkdeskService.fullSync(source.id, {
                        forceSync
                    });
                    break;
                }
                // Add other source types here as they support full sync
                default:
                    res.status(400).json({ 
                        error: 'Full sync not supported for this source type', 
                        sourceType: source.type 
                    });
                    return;
            }
            
            // Update the source's lastSyncedAt field in config
            if (syncResult.success) {
                if (!source.config) {
                    source.config = {
                        settings: {},
                        syncIntervalMinutes: 60
                    };
                }
                source.config.lastSyncedAt = new Date().toISOString();
                await repository.save(source);
            }
            
            res.json({
                success: syncResult.success,
                syncedCount: syncResult.syncedCount,
                contactsSynced: syncResult.contactsSynced,
                errors: syncResult.errors,
                lastSyncedAt: source.config?.lastSyncedAt || null
            });
        } catch (error) {
            console.error('Error during full sync:', error);
            res.status(500).json({ 
                error: 'Failed to complete full sync',
                details: error.message,
                success: false,
                syncedCount: 0,
                contactsSynced: 0,
                errors: [{ message: error.message }]
            });
        }
    }
}

// Create a singleton instance for export
export const conversationSourceController = new ConversationSourceController();
