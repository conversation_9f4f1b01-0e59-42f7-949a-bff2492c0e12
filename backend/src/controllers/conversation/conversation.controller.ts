import { Response } from 'express';
import { AuthRequest } from '../../middleware/auth.middleware';
import { AppDataSource } from '../../data-source';
import { logger } from '../../utils/logger';
import {
    Organization, Conversation, Team, ConversationParticipant,
    ConversationEmbedding, User, Project } from '../../models';

import { insightQueue, transcriptionQueue, embeddingQueue, mediaQueue } from '../../queues';
import { TeamInsightDefinition, sortTeamInsightDefinitions } from '../../utils/sorting';
import pgvector from 'pgvector';
import { ContactEventService } from '../../services/contact-event';
import { ConversationState } from '../../models/conversation-state';
import { conversationTranscriptionService } from '../../services/conversation/transcription.service';
import { conversationService } from '../../services/conversation/conversation.service';
import { projectService } from '../../services/project.service';

import { validateId } from '../../utils/response-wrapper';
import { hasPermission } from '../../decorators/permission.decorator';
import { BaseController } from '../base.controller';

export interface CreateConversationBody {
    teamId: number;
    medium: 'call' | 'video' | 'chat';
    ownerId?: number;
    contactId?: number;
    raw?: string;
    recordingPath?: string;
    recordingContentType?: string;
    customFields?: Record<string, any>;
}

interface InitializeConversationParams {
    team?: Team;
    medium: 'call' | 'video' | 'chat';
    organization: Organization;
    ownerId?: number;
    contactId?: number;
    raw?: string;
    recordingPath?: string;
    recordingContentType?: string;
    customFields?: Record<string, any>;
}

/**
 * Conversation Controller class handling all conversation-related operations
 */
export class ConversationController extends BaseController {
    private conversations = AppDataSource.getRepository(Conversation);
    private teams = AppDataSource.getRepository(Team);
    private embeddings = AppDataSource.getRepository(ConversationEmbedding);
    private users = AppDataSource.getRepository(User);
    private participants = AppDataSource.getRepository(ConversationParticipant);

    constructor() {
        super(); // Call BaseController constructor for auto-wrapping
    }
    /**
     * List all conversations for the current organization
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async findAll(req: AuthRequest, res: Response) {
        const organizationId = req.organization!.id;
        const user = req.user!;

        // Parse the teamId if it's a comma-separated string
        const queryParams = { ...req.query };
        
        // Handle teamId parsing with proper typing
        if (typeof queryParams.teamId === 'string') {
            if (queryParams.teamId.includes(',')) {
                // For teamId filter with TypeORM's 'IN' operator, we need to keep as array
                // This is handled directly in the service layer
                const teamIdArray = queryParams.teamId.split(',').map(id => parseInt(id, 10));
                // Type assertion to satisfy TypeScript, actual conversion happens in service
                queryParams.teamId = queryParams.teamId;
            } else {
                // Single teamId value as string - keep as string, service will parse it
                queryParams.teamId = queryParams.teamId;
            }
        }

        // Use the conversation service to get conversations
        const result = await conversationService.getConversations(organizationId, user, queryParams);

        res.json(result);
    }

    /**
     * Get a single conversation by ID
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async findOne(req: AuthRequest, res: Response) {
        const organizationId = req.organization!.id;
        const conversationId = validateId(req.params.id);

        // Use the conversation service to get the conversation
        const conversation = await conversationService.getConversation(conversationId, organizationId);

        res.json(conversation);
    }

    /**
     * Get transcription for a conversation
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async getTranscription(req: AuthRequest, res: Response) {
        await conversationTranscriptionService.getTranscription(req, res);
    }

    /**
     * Helper function to initialize a conversation with common setup logic
     * @param params - Parameters for initializing a conversation
     * @returns The created conversation
     * @private
     */
    private async initializeConversation(params: InitializeConversationParams): Promise<Conversation> {
        console.log('this.conversations exists in initializeConversation:', !!this.conversations);
        console.log('[CUSTOM FIELDS DEBUG] Received customFields in initializeConversation:', params.customFields);
        const conversation = this.conversations.create({
            team: params.team,
            medium: params.medium,
            organization: params.organization,
            raw: params.raw || '',
            recordingPath: params.recordingPath,
            recordingContentType: params.recordingContentType,
            processingStatus: params.recordingPath ? ConversationState.PENDING_TRANSCRIPTION : ConversationState.CREATED,
            messages: [], // Initialize empty messages array
            ownerId: params.ownerId, // Set the ownerId directly on the conversation
            customFields: params.customFields, // Set the customFields from the params
            contactId: params.contactId // Set the contactId directly on the conversation
        });

        await this.conversations.save(conversation);

        // Add owner as participant if provided
        if (params.ownerId) {
            const owner = await this.users.findOne({
                where: {
                    id: params.ownerId,
                    organizationId: params.organization.id
                }
            });

            if (owner) {
                const participant = new ConversationParticipant();
                participant.conversation = conversation;
                participant.participantType = 'user';
                participant.participantId = owner.id;
                await this.participants.save(participant);
            }
        }

        // Create a contact event if a contact is associated with this conversation
        if (params.contactId) {
            try {
                await ContactEventService.createConversationAssociationEvent(
                    conversation.id,
                    params.contactId,
                    params.ownerId
                );
            } catch (error) {
                console.error('Failed to create contact event:', error);
                // Don't throw error here, as we don't want to fail the conversation creation
                // if event creation fails
            }
        }

        // Queue embedding generation if raw text is provided
        if (params.raw) {
            // Add to embedding queue instead of processing inline
            await embeddingQueue.add({
                conversationId: conversation.id,
                text: params.raw
            });
            
            // If we have raw text but no recording, transition to PENDING_INSIGHTS
            if (!params.recordingPath) {
                await conversation.transitionTo(ConversationState.PENDING_INSIGHTS, {
                    reason: 'Text conversation with raw content'
                });
            }
        }

        // Queue transcription if recording path is provided
        if (params.recordingPath) {
            await transcriptionQueue.add({
                conversationId: conversation.id,
                filePath: params.recordingPath
            });
        }

        return conversation;
    }

    /**
     * Create a new text conversation
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:create:own')
    public async create(req: AuthRequest, res: Response) {
        try {
            console.log('this.teams exists:', !!this.teams);
            const body = req.body as CreateConversationBody;
            const organizationId = req.organization!.id;

            // Enhanced relation loading to ensure all required data is available
            const team = await this.teams.findOne({
                where: {
                    id: body.teamId,
                    organizationId
                },
                relations: [
                    'organization', 
                    'projects', 
                    'projects.insightDefinitions'
                ]
            });
            
            console.log(`Team ${body.teamId} loaded with ${team?.projects?.length || 0} projects`);

            if (!team) {
                res.status(404).json({ error: 'Team not found' });
                return;
            }

            console.log('[CUSTOM FIELDS DEBUG] Create method received customFields:', body.customFields);
            const conversation = await this.initializeConversation({
                team,
                medium: body.medium,
                organization: team.organization,
                ownerId: body.ownerId,
                contactId: body.contactId,
                raw: body.raw,
                recordingPath: body.recordingPath,
                recordingContentType: body.recordingContentType,
                customFields: body.customFields
            });
        
            console.log(`Checking insight definitions for team ${team.id} with ${team.projects.length} projects`);
        
            // Log each project and its insight definitions for debugging
            team.projects.forEach((project: Project, index) => {
                console.log(`Project ${index + 1}/${team.projects.length} (ID: ${project.id}):`);
                console.log(`- Name: ${project.name || 'unnamed'}`);
                console.log(`- Has insightDefinitions array: ${!!project.insightDefinitions}`);
                console.log(`- Number of insight definitions: ${project.insightDefinitions?.length || 0}`);
                
                // Log the first few insight definitions if they exist
                if (project.insightDefinitions && project.insightDefinitions.length > 0) {
                    project.insightDefinitions.slice(0, 3).forEach((def, i) => {
                        console.log(`  - Definition ${i + 1}: ID=${def.id}, Name=${def.name || 'unnamed'}`);
                    });
                    if (project.insightDefinitions.length > 3) {
                        console.log(`  - ... and ${project.insightDefinitions.length - 3} more definitions`);
                    }
                }
            });
        
            const hasInsightDefinitions = team.projects.some((project: Project) =>
                project.insightDefinitions && project.insightDefinitions.length > 0
            );
        
            console.log(`Team ${team.id} has insight definitions: ${hasInsightDefinitions}`);
    
            if (hasInsightDefinitions) {
                console.log(`Queueing insights for processing for conversation ${conversation.id}...`);
                // Use the conversationService to queue insights for processing
                const result = await conversationService.queueInsightsForProcessing(conversation, req.organization!.id, {
                    userId: req.user?.id
                });
                console.log(`Queueing result:`, result);
            } else {
                // No insight definitions available, mark as NO_DEFINITIONS
                await conversation.transitionTo(ConversationState.NO_DEFINITIONS, {
                    reason: 'No insight definitions available for this team',
                    teamId: team.id
                });
            }

            res.status(201).json(conversation);
        } catch (error) {
            console.error('Error creating text conversation:', error);
            res.status(500).json({
                error: 'Failed to create conversation',
                message: error.message
            });
        }
    }

    /**
     * Create an empty recording conversation
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:create:own')
    public async createRecording(req: AuthRequest, res: Response) {
        try {
            console.log('this.teams exists in createRecording:', !!this.teams);
            const { teamId, medium, ownerId, contactId, customFields } = req.body;
            console.log('[CUSTOM FIELDS DEBUG] CreateRecording method received customFields:', customFields);
            const user = req.user;

            if (!user?.organization) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }

            let team = null;
            if (teamId) {
                team = await this.teams.findOne({
                    where: {
                        id: teamId,
                        organization: { id: user.organization.id }
                    }
                });

                if (!team) {
                    res.status(404).json({ error: 'Team not found' });
                    return;
                }
            }

            const conversation = await this.initializeConversation({
                team,
                medium: medium || 'chat',
                organization: user.organization,
                ownerId,
                contactId,
                customFields
            });

            res.status(201).json(conversation);
        } catch (error) {
            console.error('Error creating recording conversation:', error);
            res.status(500).json({
                error: 'Failed to create recording conversation',
                message: error.message
            });
        }
    }

    /**
     * Reprocess insights for a conversation
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async reprocessInsights(req: AuthRequest, res: Response): Promise<void> {
        // Check if user is authenticated
        if (!req.user) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        
        const organizationId = req.organization!.id;
        const conversationId = validateId(req.params.id);
        const userId = req.user.id;

        try {
            // Use the conversation service to reprocess insights
            const result = await conversationService.reprocessInsights(conversationId, organizationId, userId);
            res.status(200).json(result);
        } catch (error) {
            res.status(404).json({ message: error.message || 'Conversation not found' });
        }
    }

    /**
     * Check insights status for a conversation
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async checkInsights(req: AuthRequest, res: Response): Promise<void> {
        const organizationId = req.organization!.id;
        const conversationId = validateId(req.params.id);

        // Use the conversation service to check insights
        const result = await conversationService.checkInsights(conversationId, organizationId);

        res.json(result);
    }

    /**
     * Check insights status for a project
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async checkProjectInsights(req: AuthRequest, res: Response): Promise<void> {
        try {
            const organizationId = req.organization!.id;
            const projectId = validateId(req.params.id);

            // Use the project service to check project insights
            const result = await projectService.checkProjectInsights(projectId, organizationId);

            res.json(result);
        } catch (error) {
            logger.error(`Error checking project insights: ${error instanceof Error ? error.message : 'Unknown error'}`);
            if (error instanceof Error && error.message.includes('Project with ID')) {
                res.status(404).json({ 
                    error: 'Project not found',
                    message: error.message 
                });
            } else {
                res.status(500).json({ 
                    error: 'Internal server error',
                    message: error instanceof Error ? error.message : 'An unexpected error occurred' 
                });
            }
        }
    }

    /**
     * Reprocess missing insights for a project
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async reprocessMissingProjectInsights(req: AuthRequest, res: Response): Promise<void> {
        const organizationId = req.organization!.id;
        const projectId = validateId(req.params.id);

        // Use the project service to reprocess missing insights
        const result = await projectService.reprocessMissingProjectInsights(projectId, organizationId);

        res.json(result);
    }

    /**
     * Delete a conversation
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:delete:own')
    public async destroy(req: AuthRequest, res: Response) {
        const conversationId = validateId(req.params.id);
        const userId = req.user?.id;
        if (!userId) {
            throw new Error('Unauthorized');
        }

        // Use the conversation service to delete the conversation
        await conversationService.deleteConversation(conversationId, userId);

        res.status(204).send();
    }

    /**
     * Count all conversations for an organization
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     */
    @hasPermission('conversations:read:own')
    public async countAll(req: AuthRequest, res: Response) {
        const organizationId = req.organization!.id;

        // Use the conversation service to count conversations
        const result = await conversationService.countConversations(organizationId);

        res.json(result);
    }

    /**
     * Update a conversation with a recording URL
     * @param req - Express request object with authenticated user and recording URL
     * @param res - Express response object
     */
    @hasPermission('conversations:update:own')
    public async updateRecordingUrl(req: AuthRequest, res: Response): Promise<void> {
        try {
            const conversationId = validateId(req.params.id);
            const { recordingUrl } = req.body;
            const organizationId = req.organization!.id;

            if (!recordingUrl) {
                res.status(400).json({ error: 'Recording URL is required' });
                return;
            }

            // Validate URL format
            try {
                new URL(recordingUrl);
            } catch (error) {
                res.status(400).json({ error: 'Invalid URL format' });
                return;
            }

            // Find the conversation
            const conversation = await this.conversations.findOne({
                where: { 
                    id: conversationId,
                    organizationId
                }
            });

            if (!conversation) {
                res.status(404).json({ error: 'Conversation not found' });
                return;
            }

            // Update conversation with recording URL
            conversation.recordingPath = recordingUrl;
            // Set a generic content type for external URLs
            conversation.recordingContentType = 'external/url';
            conversation.processingStatus = ConversationState.PENDING_TRANSCRIPTION;
            
            await this.conversations.save(conversation);

            // Queue for transcription with special flag for external URL
            await transcriptionQueue.add({
                conversationId: conversation.id,
                filePath: recordingUrl,
                isExternalUrl: true
            });
            
            // Queue for media duration extraction
            await mediaQueue.add('extract-media-duration', {
                conversationId: conversation.id
            });

            res.status(200).json({ 
                message: 'Recording URL added successfully', 
                status: 'processing'
            });
        } catch (error) {
            console.error('Error updating recording URL:', error);
            res.status(500).json({ 
                error: 'Failed to update recording URL',
                message: error.message
            });
        }
    }
}

// Create a singleton instance for export
export const conversationController = (() => {
    const instance = new ConversationController();

    // Explicitly bind all methods to the instance
    const methodsToBind = [
        'create',
        'createRecording',
        'initializeConversation',
        'findAll',
        'findOne',
        'getTranscription',
        'reprocessInsights',
        'checkInsights',
        'checkProjectInsights',
        'reprocessMissingProjectInsights',
        'destroy',
        'countAll',
        'updateRecordingUrl'
    ];

    methodsToBind.forEach(method => {
        if (typeof instance[method] === 'function') {
            instance[method] = instance[method].bind(instance);
        }
    });

    return instance;
})();
