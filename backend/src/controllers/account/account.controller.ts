import { Response, NextFunction } from 'express';
import { Account } from '../../models/account';
import { Contact } from '../../models/contact';
import { AuthRequest } from '../../middleware/auth.middleware';
import { AppDataSource } from '../../data-source';
import { hasPermission } from '../../decorators/permission.decorator';
import { audited } from '../../decorators/audit.decorator';
import { BaseController } from '../base.controller';

interface CreateAccountBody {
    name: string;
    sourceId?: number;
    sourceExternalId?: string;
    customFields?: Record<string, any>;
}

interface UpdateAccountBody extends Partial<CreateAccountBody> {}

/**
 * Account Controller class handling all account-related operations
 */
export class AccountController extends BaseController {
    private accounts = AppDataSource.getRepository(Account);
    private contacts = AppDataSource.getRepository(Contact);

    constructor() {
        super(); // Call BaseController constructor for auto-wrapping
    }
    /**
     * List all accounts for an organization
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('accounts:read:own')
    @audited({
        action: 'read',
        resource: 'account',
        includeRequest: false,
        includeResponse: false
    })
    public async findAll(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const page = parseInt(req.query.page as string) || 1;
            const perPage = parseInt(req.query.perPage as string) || 10;
            const unpaginated = req.query.unpaginated === 'true';

            const queryBuilder = this.accounts.createQueryBuilder('account')
                .where('account.organizationId = :orgId', { orgId: this.organizationId })
                .orderBy('account.createdAt', 'DESC');

            // Get total count
            const totalItems = await queryBuilder.getCount();

            // If unpaginated, return all items
            if (unpaginated) {
                const accounts = await queryBuilder.getMany();
                res.json({
                    data: accounts,
                    meta: {
                        currentPage: 1,
                        totalPages: 1,
                        totalItems,
                        perPage: totalItems
                    }
                });
                return;
            }

            // Apply pagination
            const skip = (page - 1) * perPage;
            const accounts = await queryBuilder
                .skip(skip)
                .take(perPage)
                .getMany();

            const totalPages = Math.ceil(totalItems / perPage);

            res.json({
                data: accounts,
                meta: {
                    currentPage: page,
                    totalPages,
                    totalItems,
                    perPage
                }
            });
        } catch (error) {
            console.error('Error listing accounts:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }

    /**
     * Create a new account
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('accounts:create:own')
    @audited({
        action: 'create',
        resource: 'account',
        includeRequest: true,
        includeResponse: true
    })
    public async create(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const { name, sourceId, sourceExternalId, customFields } = req.body as CreateAccountBody;

            if (!name) {
                res.status(400).json({ error: 'Name is required' });
                return;
            }

            const account = await Account.createAccount({
                name,
                organizationId: this.organizationId,
                sourceId,
                sourceExternalId,
                customFields
            });

            res.status(201).json(account);
        } catch (error) {
            console.error('Error creating account:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }

    /**
     * Get a single account by ID
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('accounts:read:own')
    @audited({
        action: 'read',
        resource: 'account',
        entityIdPath: 'params.id',
        includeRequest: false,
        includeResponse: true
    })
    public async findOne(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const accountId = parseInt(req.params.id);
            const account = await Account.getAccount(accountId, this.organizationId);

            if (!account) {
                res.status(404).json({ error: 'Account not found' });
                return;
            }

            res.json(account);
        } catch (error) {
            console.error('Error finding account:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }

    /**
     * Update an existing account
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('accounts:update:own')
    @audited({
        action: 'update',
        resource: 'account',
        entityIdPath: 'params.id',
        includeRequest: true,
        includeResponse: true,
        capturePreState: true
    })
    public async update(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const accountId = parseInt(req.params.id);
            const { name, sourceId, sourceExternalId, customFields } = req.body as UpdateAccountBody;

            if (!name) {
                res.status(400).json({ error: 'Name is required' });
                return;
            }

            const updatedAccount = await Account.updateAccount(
                accountId,
                this.organizationId,
                {
                    name,
                    sourceId,
                    sourceExternalId,
                    customFields
                }
            );

            if (!updatedAccount) {
                res.status(404).json({ error: 'Account not found' });
                return;
            }

            res.json(updatedAccount);
        } catch (error) {
            console.error('Error updating account:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }

    /**
     * Delete an account
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('accounts:delete:own')
    @audited({
        action: 'delete',
        resource: 'account',
        entityIdPath: 'params.id',
        capturePreState: true,
        capturePostState: false,
        auditOnError: true
    })
    public async destroy(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const accountId = parseInt(req.params.id);
            const deleted = await Account.deleteAccount(accountId, this.organizationId);

            if (!deleted) {
                res.status(404).json({ error: 'Account not found' });
                return;
            }

            res.status(204).send();
        } catch (error) {
            console.error('Error deleting account:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }

    /**
     * Count all accounts for an organization
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('accounts:read:own')
    public async countAccounts(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const count = await this.accounts.count({
                where: {
                    organizationId: this.organizationId
                }
            });

            res.json({ count });
        } catch (error) {
            console.error('Error counting accounts:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }

    /**
     * Get all contacts assigned to a specific account
     * @param req - Express request object with authenticated user
     * @param res - Express response object
     * @param next - Express next function
     */
    @hasPermission('contacts:read:own')
    @audited({
        action: 'read',
        resource: 'contact',
        includeRequest: false,
        includeResponse: false
    })
    public async getAccountContacts(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
        try {
            const accountId = parseInt(req.params.id);
            
            if (isNaN(accountId)) {
                res.status(400).json({ error: 'Invalid account ID' });
                return;
            }

            // First verify the account exists and belongs to the organization
            const account = await this.accounts.findOne({
                where: { 
                    id: accountId, 
                    organizationId: this.organizationId 
                }
            });

            if (!account) {
                res.status(404).json({ error: 'Account not found' });
                return;
            }

            // Get pagination parameters
            const page = parseInt(req.query.page as string) || 1;
            const perPage = parseInt(req.query.perPage as string) || 10;
            const unpaginated = req.query.unpaginated === 'true';

            const queryBuilder = this.contacts.createQueryBuilder('contact')
                .where('contact.accountId = :accountId', { accountId })
                .andWhere('contact.organizationId = :orgId', { orgId: this.organizationId })
                .orderBy('contact.createdAt', 'DESC');

            // Get total count
            const totalItems = await queryBuilder.getCount();

            // If unpaginated, return all items
            if (unpaginated) {
                const contacts = await queryBuilder.getMany();
                res.json({
                    data: contacts,
                    meta: {
                        currentPage: 1,
                        totalPages: 1,
                        totalItems,
                        perPage: totalItems
                    }
                });
                return;
            }

            // Apply pagination
            const skip = (page - 1) * perPage;
            const contacts = await queryBuilder
                .skip(skip)
                .take(perPage)
                .getMany();

            const totalPages = Math.ceil(totalItems / perPage);

            res.json({
                data: contacts,
                meta: {
                    currentPage: page,
                    totalPages,
                    totalItems,
                    perPage
                }
            });
        } catch (error) {
            console.error('Error getting account contacts:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
}

// Create a singleton instance for export
export const accountController = new AccountController();
