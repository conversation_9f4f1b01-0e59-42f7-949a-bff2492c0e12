#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

echo "Running on-create.sh script..."

# Function to wait for Redis to be ready
wait_for_redis() {
  echo "Waiting for Redis to be ready..."
  until nc -z redis 6379; do
    echo "Redis is not available yet..."
    sleep 1
  done
  echo "Redis is ready!"
}

# Ensure Redis is available
wait_for_redis

# Function for colorful output
function log() {
    local BLUE='\033[0;34m'
    local GREEN='\033[0;32m'
    local RED='\033[0;31m'
    local YELLOW='\033[0;33m'
    local RESET='\033[0m'
    local COLOR=$BLUE
    local TYPE="info"

    if [ "$2" = "success" ]; then
        COLOR=$GREEN
        TYPE="success"
    elif [ "$2" = "error" ]; then
        COLOR=$RED
        TYPE="error"
    elif [ "$2" = "warning" ]; then
        COLOR=$YELLOW
        TYPE="warning"
    fi

    echo -e "${COLOR}[on-create.sh] ${TYPE}:${RESET} $1"
}

log "Starting environment setup..."

# Fix SSH config for Linux container (remove macOS-specific options)
if [ -f "/home/<USER>/.ssh/config" ]; then
    log "Fixing SSH config for Linux container..."
    # Create a backup
    cp /home/<USER>/.ssh/config /home/<USER>/.ssh/config.backup
    # Remove macOS-specific options that cause issues in Linux
    grep -v -i "usekeychain" /home/<USER>/.ssh/config.backup > /home/<USER>/.ssh/config || true
    # Ensure proper permissions
    chmod 600 /home/<USER>/.ssh/config
    log "SSH config fixed for Linux container" "success"
fi

# Define paths
PROJECT_ROOT="/app"

# Create .env files from examples if not exist

# Root .env
if [ ! -f "$PROJECT_ROOT/.env" ] && [ -f "$PROJECT_ROOT/.env.example" ]; then
    log "Creating .env from .env.example"
    cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
fi

# Backend .env
if [ ! -f "$PROJECT_ROOT/backend/.env" ] && [ -f "$PROJECT_ROOT/backend/.env.example" ]; then
    log "Creating backend/.env from backend/.env.example"
    cp "$PROJECT_ROOT/backend/.env.example" "$PROJECT_ROOT/backend/.env"
fi

# Frontend .env
if [ ! -f "$PROJECT_ROOT/frontend/.env" ] && [ -f "$PROJECT_ROOT/frontend/.env.example" ]; then
    log "Creating frontend/.env from frontend/.env.example"
    cp "$PROJECT_ROOT/frontend/.env.example" "$PROJECT_ROOT/frontend/.env"
fi

# Install project dependencies
log "Installing dependencies as node user..."

# Make sure we're running as the node user for proper permissions
if [ "$(id -u)" = "0" ]; then
    log "Switching to node user for npm operations..."
    exec su node -c "bash $0"
    exit $?
fi

# Add node_modules/.bin to PATH for all subsequent commands
export PATH="$PROJECT_ROOT/node_modules/.bin:$PROJECT_ROOT/frontend/node_modules/.bin:$PROJECT_ROOT/backend/node_modules/.bin:$PATH"

# Root dependencies
if [ -f "/app/package.json" ]; then
    log "Installing root dependencies..."
    cd /app
    npm install
    log "Root dependencies installed successfully" "success"
fi

# Backend dependencies
if [ -f "/app/backend/package.json" ]; then
    log "Installing backend dependencies..."
    cd /app/backend
    npm install
    log "Backend dependencies installed successfully" "success"
fi

# Frontend dependencies
if [ -f "/app/frontend/package.json" ]; then
    log "Installing frontend dependencies..."
    cd /app/frontend
    npm install
    
    # Verify Vite is installed
    if ! [ -f "/app/frontend/node_modules/.bin/vite" ]; then
        log "WARNING: Vite executable not found after npm install. Installing vite explicitly..." "warning"
        npm install vite --save-dev
        
        # Verify installation succeeded
        if [ -f "/app/frontend/node_modules/.bin/vite" ]; then
            log "Vite successfully installed manually" "success"
        else
            log "Failed to install Vite. Please check package.json and npm configurations" "error"
        fi
    else
        log "Vite executable found at /app/frontend/node_modules/.bin/vite" "success"
    fi
    
    log "Frontend dependencies installed successfully" "success"
fi

# Return to root directory
cd /app

log "Environment setup complete!" "success"
log "You can now start the application using: cd frontend && npm run dev"
