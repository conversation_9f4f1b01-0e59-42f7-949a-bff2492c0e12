{"name": "Luno Development Environment", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/app", "forwardPorts": [3000, 4000, 5000, 5173], "customizations": {"vscode": {"extensions": ["ms-azuretools.vscode-docker", "vscode.git", "vscode.git-base", "vscode.github", "vscode.github-authentication", "GitHub.vscode-pull-request-github", "vscode.typescript-language-features", "vscode.npm", "vscode.json-language-features", "Tailscale.vscode-tailscale", "Augment.vscode-augment", "Anthropic.claude-code", "Zignd.html-css-class-completion", "RooVeterinaryInc.roo-cline", "sitoi.ai-commit"], "settings": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "typescript.tsdk": "node_modules/typescript/lib"}}}, "postCreateCommand": "bash .devcontainer/on-create.sh", "remoteUser": "node"}