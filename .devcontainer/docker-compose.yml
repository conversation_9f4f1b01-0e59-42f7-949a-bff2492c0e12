version: '3'

services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      # Mount the root project directory to /app in the container
      - ..:/app:cached
      # Exclude node_modules directories from the mount to avoid permission issues
      - /app/node_modules
      - /app/backend/node_modules
      - /app/frontend/node_modules
    command: sleep infinity
    ports:
      - "3000:3000"  # Backend API port
      - "4000:4000"  # Backend worker port
      - "5000:5000"  # Frontend port
      - "5173:5173"  # Vite dev server port
    environment:
      - NODE_ENV=development
      - VITE_MOTHERSHIP_URL=http://localhost:3000
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    # Add any additional environment variables here

volumes:
  redis-data:
