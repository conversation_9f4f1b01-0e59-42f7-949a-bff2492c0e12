# Luno Development Container

This directory contains configuration files for setting up a consistent development environment using Visual Studio Code's [Remote - Containers](https://code.visualstudio.com/docs/remote/containers) extension.

## Prerequisites

1. [Docker Desktop](https://www.docker.com/products/docker-desktop)
2. [Visual Studio Code](https://code.visualstudio.com/)
3. [Remote - Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)

## Getting Started

1. Open VS Code
2. Click on the green icon in the bottom-left corner of VS Code
3. Select "Remote-Containers: Open Folder in Container..."
4. Navigate to the root of this repository and click "Open"

VS Code will build the container based on the configuration in this directory and open the project inside the container.

## Features

- Node.js 20 development environment
- Pre-configured VS Code settings and extensions
- Automatic port forwarding for development servers:
  - Frontend: Port 5000
  - Backend API: Port 4000
  - Backend Worker: Port 3000
- Volume mounting for efficient file editing
- Named volumes for `node_modules` to avoid performance issues

## Development Workflow

Once inside the container, you can run the following commands to start the development servers:

```bash
# Start frontend development server
npm run dev:frontend

# Start backend development server
npm run dev:backend

# Start worker
npm run dev:worker

# Or start all services at once
npm run dev
```

## Customization

You can customize the development container by modifying the following files:

- `.devcontainer/devcontainer.json`: VS Code settings and extensions
- `.devcontainer/Dockerfile`: Container image configuration
- `.devcontainer/docker-compose.yml`: Multi-container setup and volume configuration
