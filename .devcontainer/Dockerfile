FROM --platform=linux/amd64 node:20-alpine

# Install necessary tools for development
RUN apk add --no-cache git curl bash netcat-openbsd gnupg

WORKDIR /app

# Set environment variables for development
ENV NODE_ENV=development
ENV VITE_MOTHERSHIP_URL=http://localhost:3000

# Create node_modules directories with correct permissions
RUN mkdir -p /app/node_modules /app/backend/node_modules /app/frontend/node_modules \
    && chown -R node:node /app

# Setup will be completed by the on-create.sh script after container is running
# This approach resolves the permission issues with node_modules

EXPOSE 3000 4000 5000 5173

# Make sure we actually have bash available for scripting
CMD ["sleep", "infinity"]
