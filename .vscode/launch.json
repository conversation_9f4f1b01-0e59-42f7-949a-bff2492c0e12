{"version": "0.2.0", "compounds": [{"name": "All Development Servers", "configurations": ["Frontend", "Backend", "Worker"], "stopAll": true}, {"name": "All Development Servers + Chrome Debug", "configurations": ["Frontend", "Backend", "Worker", "Chrome"], "stopAll": true}], "configurations": [{"name": "Backend", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/backend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev:backend"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--enable-source-maps"}}, {"name": "Frontend", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal"}, {"name": "Chrome", "type": "chrome", "request": "launch", "url": "https://localhost:5173", "webRoot": "${workspaceFolder}/frontend", "sourceMapPathOverrides": {"/@fs/*": "${webRoot}/*", "/*": "${webRoot}/*"}}, {"name": "Worker", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/backend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev:worker"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal"}]}