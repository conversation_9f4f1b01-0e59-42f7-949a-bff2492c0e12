name: Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install Dependencies
      run: npm ci

    - name: Install Frontend Dependencies
      run: cd frontend && npm ci
        
    - name: Run Frontend Tests
      run: npm run test:frontend

  backend-tests:
    runs-on: ubuntu-latest
    env:
      JWT_SECRET: dummy
      OPENAI_API_KEY: dummy
      LAMINAR_API_KEY: dummy
      AWS_ACCESS_KEY_ID: dummy
      AWS_SECRET_ACCESS_KEY: dummy
      DEEPGRAM_API_KEY: dummy
      ASSEMBLY_AI_API_KEY: dummy
      RESEND_API_KEY: dummy
      HUBSPOT_CLIENT_ID: dummy
      HUBSPOT_CLIENT_SECRET: dummy
      TALKDESK_CLIENT_ID: dummy
      TALKDESK_CLIENT_SECRET: dummy
      ZOOM_CLIENT_ID: dummy
      ZOOM_CLIENT_SECRET: dummy
      NODE_ENV: test
    
    services:
      postgres:
        image: pgvector/pgvector:pg15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install Root Dependencies
      run: npm ci
        
    - name: Install Backend Dependencies
      run: cd backend && npm ci

    - name: Install PostgreSQL client
      run: |
        sudo apt-get update
        sudo apt-get install -y postgresql-client

    - name: Setup Test Database
      run: |
        PGPASSWORD=postgres psql -h localhost -U postgres -d postgres -c "DROP DATABASE IF EXISTS luno_test;"
        PGPASSWORD=postgres psql -h localhost -U postgres -d postgres -c "CREATE DATABASE luno_test;"
        
    - name: Run TypeORM Migrations
      run: |
        cd backend
        npx typeorm-ts-node-commonjs migration:run -d src/data-source.ts
      env:
        POSTGRES_HOST: localhost
        POSTGRES_PORT: 5432
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        POSTGRES_DB: luno_test
        NODE_ENV: test
        
    - name: Run Backend Tests
      run: cd backend && npm run test
      env:
        POSTGRES_HOST: localhost
        POSTGRES_PORT: 5432
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        POSTGRES_DB: luno_test
        NODE_ENV: test
